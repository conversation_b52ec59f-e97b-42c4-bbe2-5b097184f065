using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using Application.RoleActions.Commands;
using Application.RoleActions.DTOs;
using Application.RoleActions.Queries;
using Mapster;
using MediatR;
using Shared.Common.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Infrastructure.OpenApi;

namespace Web.Host.Controllers.Identity;

/// <summary>
/// Controller for role operations
/// </summary>
public class RolesController : BaseApiController
{
    /// <summary>
    /// Role service
    /// </summary>
    private readonly IRoleService _roleService;

    /// <summary>
    /// Mediator
    /// </summary>
    private readonly IMediator _mediator;

    /// <summary>
    /// Constructor
    /// </summary>
    public RolesController(IRoleService roleService, IMediator mediator)
    {
        _roleService = roleService;
        _mediator = mediator;
    }

    /// <summary>
    /// Get all roles filtered by ProductId
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<RoleDto>>>> GetAllAsync([FromQuery] Guid productId)
    {
        try
        {
            if (productId == Guid.Empty)
            {
                return BadRequest(Result<List<RoleDto>>.Failure("ProductId is required and cannot be empty."));
            }

            var roles = await _roleService.GetListAsync(productId);
            return Ok(Result<List<RoleDto>>.Success(roles));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<List<RoleDto>>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Get a role by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<RoleDto>>> GetByIdAsync(Guid id)
    {
        try
        {
            var role = await _roleService.GetByIdAsync(id.ToString());
            if (role == null)
            {
                return NotFound(Result<RoleDto>.Failure($"Role with ID {id} not found."));
            }

            return Ok(Result<RoleDto>.Success(role));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<RoleDto>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Create a new role
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> CreateAsync(CreateRoleRequest request)
    {
        try
        {
            var result = await _roleService.CreateAsync(request);
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Update a role
    /// </summary>
    [HttpPut]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> UpdateAsync(UpdateRoleRequest request)
    {
        try
        {
            var result = await _roleService.UpdateAsync(request);
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Delete a role
    /// </summary>
    [HttpDelete("{id:guid}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> DeleteAsync(Guid id)
    {
        try
        {
            var result = await _roleService.DeleteAsync(id.ToString());
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Get role actions
    /// </summary>
    [HttpGet("{roleId:guid}/actions")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<RoleActionsResponse>>> GetRoleActionsAsync(Guid roleId)
    {
        try
        {
            var query = new GetRoleActionsQuery(roleId);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(Result<RoleActionsResponse>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Update role actions
    /// </summary>
    [HttpPut("{roleId:guid}/actions")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<RoleActionsResponse>>> UpdateRoleActionsAsync(Guid roleId, [FromBody] List<Guid> actionIds)
    {
        try
        {
            var command = new UpdateRoleActionsCommand
            {
                RoleId = roleId,
                ActionIds = actionIds
            };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(Result<RoleActionsResponse>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Bulk update role actions
    /// </summary>
    [HttpPut("bulk/actions")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<RoleActionsResponse>>>> BulkUpdateRoleActionsAsync([FromBody] BulkUpdateRoleActionsRequest request)
    {
        try
        {
            var command = request.Adapt<BulkUpdateRoleActionsCommand>();
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(Result<List<RoleActionsResponse>>.Failure(ex.Message));
        }
    }
}
