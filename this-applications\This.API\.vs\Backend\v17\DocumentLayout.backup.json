{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\dtos\\objectwithactionsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\dtos\\objectwithactionsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\mappings\\objectmappings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\mappings\\objectmappings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\queries\\getobjectswithactionsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\queries\\getobjectswithactionsquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\queries\\getobjectswithactionsqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\queries\\getobjectswithactionsqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\specifications\\objectswithactionsspec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\specifications\\objectswithactionsspec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\specifications\\displayactionsbyobjectidsspec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\specifications\\displayactionsbyobjectidsspec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\applicationdbinitializer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\applicationdbinitializer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\comprehensiveentitycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\comprehensiveentitycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\dockerfile||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\dockerfile||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\objectvalueconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\objectvalueconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\objectlookupconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\objectlookupconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\objectconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\objectconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\objectvaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\objectvaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\lookup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\lookup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\objectlookup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\objectlookup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\metadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\metadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\identity\\commands\\bulkupdateuserrolescommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\identity\\commands\\bulkupdateuserrolescommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\identity\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\identity\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\identity\\commands\\bulkcreateuserscommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\identity\\commands\\bulkcreateuserscommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\context\\specifications\\contextspecifications.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\context\\specifications\\contextspecifications.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\contextcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\contextcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\subscriptionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\subscriptionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\sample-object-payloads.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\sample-object-payloads.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\appartmentinventory.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\appartmentinventory.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\sample-bulk-payload.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\sample-bulk-payload.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\datatype.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\datatype.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-with-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-with-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-structured-hierarchy.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-structured-hierarchy.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-real-estate-with-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-real-estate-with-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-apartment-values-requests.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-apartment-values-requests.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-apartment-values-guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-apartment-values-guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-apartment-values-all-objects.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-apartment-values-all-objects.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-all-objects-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-all-objects-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-all-13-objects-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-all-13-objects-values.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment-sample-data-readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment-sample-data-readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment-bulk-upsert-requests.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment-bulk-upsert-requests.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment_3d_template.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment_3d_template.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment_3d_structure_guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment_3d_structure_guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment_3d_complex_example.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment_3d_complex_example.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\all-objects-metadata-mapping.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\all-objects-metadata-mapping.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\repositories\\dapperrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\repositories\\dapperrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objectvalues\\queries\\getobjectinstanceview\\getobjectinstanceviewqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objectvalues\\queries\\getobjectinstanceview\\getobjectinstanceviewqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\objectscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\objectscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\configurations\\database.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\configurations\\database.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\comprehensiveentitydata\\dtos\\comprehensiveentitydatadto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\comprehensiveentitydata\\dtos\\comprehensiveentitydatadto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\repositories\\hierarchicalentitydatarepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\repositories\\hierarchicalentitydatarepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\repositories\\hierarchicalentitydataqueries.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\repositories\\hierarchicalentitydataqueries.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\comprehensiveentitydata\\queries\\gethierarchicalentitydataqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\comprehensiveentitydata\\queries\\gethierarchicalentitydataqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\configurations\\database.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\configurations\\database.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\configurations\\database.prd.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\configurations\\database.prd.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\configurations\\database.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\configurations\\database.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{66C8373D-5EA7-4BAB-9139-C45C4CFBC15C}|Migrators\\Migrators.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\migrators\\migrations\\application\\applicationdbcontextmodelsnapshot.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{66C8373D-5EA7-4BAB-9139-C45C4CFBC15C}|Migrators\\Migrators.csproj|solutionrelative:migrators\\migrations\\application\\applicationdbcontextmodelsnapshot.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\datatype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\datatype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\productvalueconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\productvalueconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\subscriptionconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\subscriptionconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\templateconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\templateconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\subscription.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\subscription.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\templatescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\templatescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\products\\commands\\createproductwithsubscriptioncommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\products\\commands\\createproductwithsubscriptioncommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\productscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\productscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\repositories\\repositorybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\repositories\\repositorybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Api.Host\\Controllers\\ContextController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Api.Host\\Controllers\\ContextController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\datatypes\\queries\\getdatatypebyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\datatypes\\queries\\getdatatypebyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\datatypes\\queries\\getdatatypesquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\datatypes\\queries\\getdatatypesquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\datatypes\\specifications\\datatypebynamespec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\datatypes\\specifications\\datatypebynamespec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\repositories\\hierarchicalentitydatamappingdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\repositories\\hierarchicalentitydatamappingdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\datatypes\\commands\\createdatatypecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\datatypes\\commands\\createdatatypecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\metadatamanagement\\dtos\\metadatadto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\metadatamanagement\\dtos\\metadatadto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\complete-hierarchy-request.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\complete-hierarchy-request.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment-values-sample-data.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment-values-sample-data.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment-sample-data.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment-sample-data.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\apartment-api-usage-guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\apartment-api-usage-guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\metadataconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\metadataconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6E2C286E-B6CA-495A-AA46-64941863E7B8}|Abstraction\\Abstraction.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\abstraction\\database\\repositories\\irepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6E2C286E-B6CA-495A-AA46-64941863E7B8}|Abstraction\\Abstraction.csproj|solutionrelative:abstraction\\database\\repositories\\irepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6E2C286E-B6CA-495A-AA46-64941863E7B8}|Abstraction\\Abstraction.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\abstraction\\common\\dynamicoperationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6E2C286E-B6CA-495A-AA46-64941863E7B8}|Abstraction\\Abstraction.csproj|solutionrelative:abstraction\\common\\dynamicoperationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objectvalues\\dtos\\objectvaluesdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objectvalues\\dtos\\objectvaluesdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\repositories\\objectvaluesrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\repositories\\objectvaluesrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\startup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\startup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\services\\hierarchicalentitydataservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\services\\hierarchicalentitydataservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\startup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\startup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\multitenancy\\queries\\gettenantwithproductqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\multitenancy\\queries\\gettenantwithproductqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\multitenancy\\queries\\gettenantwithproductquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\multitenancy\\queries\\gettenantwithproductquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\assemblyreference.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\assemblyreference.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\class1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\class1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\usermetadatamanagement\\specifications\\usermetadatabyuserandmetadataspec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\usermetadatamanagement\\specifications\\usermetadatabyuserandmetadataspec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\common\\contracts\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\common\\contracts\\baseentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\common\\contracts\\auditableentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\common\\contracts\\auditableentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\object.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\object.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\commands\\upsertobjectwithmetadata\\upsertobjectwithmetadatacommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\commands\\upsertobjectwithmetadata\\upsertobjectwithmetadatacommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\commands\\upsertobjectwithmetadata\\upsertobjectwithmetadatabulkcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\commands\\upsertobjectwithmetadata\\upsertobjectwithmetadatabulkcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\commands\\upsertobjectwithmetadata\\upsertobjectwithmetadatacommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\commands\\upsertobjectwithmetadata\\upsertobjectwithmetadatacommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objectvalues\\commands\\upsertbulk\\upsertbulkobjectvaluecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objectvalues\\commands\\upsertbulk\\upsertbulkobjectvaluecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\specifications\\objectbyfeatureandnamespec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\specifications\\objectbyfeatureandnamespec.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\queries\\getobjectsqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\queries\\getobjectsqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\queries\\getobjectbyidwithmetadataqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\queries\\getobjectbyidwithmetadataqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objects\\queries\\getobjectbyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objects\\queries\\getobjectbyidqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\common\\commands\\dynamicvalueoperationcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\common\\commands\\dynamicvalueoperationcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\metadatacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\metadatacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\integrationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\integrationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\integrationconfigurationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\integrationconfigurationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\integrationapiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\integrationapiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\fieldmappingscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\fieldmappingscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\datatypescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\datatypescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\datatransformationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\datatransformationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\common\\contracts\\ientity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\common\\contracts\\ientity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\common\\contracts\\isoftdelete.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\common\\contracts\\isoftdelete.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\common\\contracts\\iauditableentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\common\\contracts\\iauditableentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\common\\contracts\\iaggregateroot.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\common\\contracts\\iaggregateroot.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\common\\contracts\\domainevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\common\\contracts\\domainevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\identity\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\identity\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\userrole.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\userrole.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\uservalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\uservalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\usermetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\usermetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\tenantinfovalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\tenantinfovalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\tenantinfometadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\tenantinfometadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\multitenancy\\apptenantinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\multitenancy\\apptenantinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\synchistory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\synchistory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\subscriptionvalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\subscriptionvalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\subscriptionmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\subscriptionmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\rolevalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\rolevalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\rolemetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\rolemetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\role.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\role.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\productvalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\productvalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\productmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\productmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\objectvalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\objectvalue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\objectmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\objectmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\integrationconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\integrationconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\integrationapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\integrationapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\integration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\integration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\fieldmapping.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\fieldmapping.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\domain\\entities\\conflictresolution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\conflictresolution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\baseapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\baseapicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6E2C286E-B6CA-495A-AA46-64941863E7B8}|Abstraction\\Abstraction.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\abstraction\\database\\repositories\\idapperrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6E2C286E-B6CA-495A-AA46-64941863E7B8}|Abstraction\\Abstraction.csproj|solutionrelative:abstraction\\database\\repositories\\idapperrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\objectmetadataconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\objectmetadataconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\shared\\common\\agenttracking.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A70DF8F-2ADA-476F-81D2-96D0BEB74445}|Shared\\Shared.csproj|solutionrelative:shared\\common\\agenttracking.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\application\\objectvalues\\commands\\createobjectvaluecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\objectvalues\\commands\\createobjectvaluecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\controllers\\identity\\tokenscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\identity\\tokenscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\web.host\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\roleconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\roleconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\rolemetadataconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\rolemetadataconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\basedbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\basedbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\extensions\\modelbuilderextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\extensions\\modelbuilderextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\userconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\userconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\uservalueconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\uservalueconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\fieldmappingconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\fieldmappingconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|c:\\users\\<USER>\\desktop\\leadrat\\this-applications\\this.api\\infrastructure\\database\\configuration\\entities\\identityconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9FE0B7A5-10A7-438F-8CC1-BA31A0BB3DA3}|Infrastructure\\Infrastructure.csproj|solutionrelative:infrastructure\\database\\configuration\\entities\\identityconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\correct-metadata-mapping.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Shared\\Common\\correct-metadata-mapping.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\corrected-apartment-values-all-objects.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Shared\\Common\\corrected-apartment-values-all-objects.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\hierarchical-relationships-summary.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Shared\\Common\\hierarchical-relationships-summary.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\objectvalues-upsert-behavior.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Shared\\Common\\objectvalues-upsert-behavior.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ComprehensiveEntityDataController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Web.Host\\Controllers\\ComprehensiveEntityDataController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 290, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 46, "Title": "HierarchicalEntityDataRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\HierarchicalEntityDataRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Repositories\\HierarchicalEntityDataRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\HierarchicalEntityDataRepository.cs", "RelativeToolTip": "Infrastructure\\Database\\Repositories\\HierarchicalEntityDataRepository.cs", "ViewState": "AgIAAJYDAAAAAAAAAAAUwAAFAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T11:51:33.348Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:676956467:0:{83107a3e-496a-485e-b455-16ddb978e55e}"}, {"$type": "Bookmark", "Name": "ST:350619200:0:{83107a3e-496a-485e-b455-16ddb978e55e}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ObjectWithActionsDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\DTOs\\ObjectWithActionsDto.cs", "RelativeDocumentMoniker": "Application\\Objects\\DTOs\\ObjectWithActionsDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\DTOs\\ObjectWithActionsDto.cs", "RelativeToolTip": "Application\\Objects\\DTOs\\ObjectWithActionsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:58:27.372Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ObjectMappings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Mappings\\ObjectMappings.cs", "RelativeDocumentMoniker": "Application\\Objects\\Mappings\\ObjectMappings.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Mappings\\ObjectMappings.cs", "RelativeToolTip": "Application\\Objects\\Mappings\\ObjectMappings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:58:26.35Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GetObjectsWithActionsQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectsWithActionsQuery.cs", "RelativeDocumentMoniker": "Application\\Objects\\Queries\\GetObjectsWithActionsQuery.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectsWithActionsQuery.cs", "RelativeToolTip": "Application\\Objects\\Queries\\GetObjectsWithActionsQuery.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:58:23.699Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "GetObjectsWithActionsQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectsWithActionsQueryHandler.cs", "RelativeDocumentMoniker": "Application\\Objects\\Queries\\GetObjectsWithActionsQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectsWithActionsQueryHandler.cs", "RelativeToolTip": "Application\\Objects\\Queries\\GetObjectsWithActionsQueryHandler.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAABUAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:58:16.363Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DisplayActionsByObjectIdsSpec.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Specifications\\DisplayActionsByObjectIdsSpec.cs", "RelativeDocumentMoniker": "Application\\Objects\\Specifications\\DisplayActionsByObjectIdsSpec.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Specifications\\DisplayActionsByObjectIdsSpec.cs", "RelativeToolTip": "Application\\Objects\\Specifications\\DisplayActionsByObjectIdsSpec.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:58:01.746Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ObjectsWithActionsSpec.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Specifications\\ObjectsWithActionsSpec.cs", "RelativeDocumentMoniker": "Application\\Objects\\Specifications\\ObjectsWithActionsSpec.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Specifications\\ObjectsWithActionsSpec.cs", "RelativeToolTip": "Application\\Objects\\Specifications\\ObjectsWithActionsSpec.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:58:00.733Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ObjectValueConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectValueConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\ObjectValueConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectValueConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\ObjectValueConfig.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAYwDMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T13:37:24.476Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ApplicationDbInitializer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\ApplicationDbInitializer.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\ApplicationDbInitializer.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\ApplicationDbInitializer.cs", "RelativeToolTip": "Infrastructure\\Database\\ApplicationDbInitializer.cs", "ViewState": "AgIAABEAAAAAAAAAAAAuwCgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T16:45:56.984Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Web.Host\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Properties\\launchSettings.json", "RelativeToolTip": "Web.Host\\Properties\\launchSettings.json", "ViewState": "AgIAAAMAAAAAAAAAAAAAABcAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-09T06:18:29.825Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ObjectConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\ObjectConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\ObjectConfig.cs", "ViewState": "AgIAACwAAAAAAAAAAAAjwDsAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:09:45.475Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ComprehensiveEntityController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ComprehensiveEntityController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ComprehensiveEntityController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ComprehensiveEntityController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ComprehensiveEntityController.cs", "ViewState": "AgIAANkAAAAAAAAAAADwv9IAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T10:40:59.114Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "Lookup.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Lookup.cs", "RelativeDocumentMoniker": "Domain\\Entities\\Lookup.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Lookup.cs", "RelativeToolTip": "Domain\\Entities\\Lookup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:20:01.792Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "Dockerfile", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Dockerfile", "RelativeDocumentMoniker": "Web.Host\\Dockerfile", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Dockerfile", "RelativeToolTip": "Web.Host\\Dockerfile", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-16T10:24:04.799Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "ObjectLookupConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectLookupConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\ObjectLookupConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectLookupConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\ObjectLookupConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T14:05:43.589Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "ObjectLookup.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ObjectLookup.cs", "RelativeDocumentMoniker": "Domain\\Entities\\ObjectLookup.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ObjectLookup.cs", "RelativeToolTip": "Domain\\Entities\\ObjectLookup.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T14:04:15.163Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ObjectValuesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ObjectValuesController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ObjectValuesController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ObjectValuesController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ObjectValuesController.cs", "ViewState": "AgIAAIYBAAAAAAAAAAAiwLMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:47:19.199Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "UsersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\Identity\\UsersController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\Identity\\UsersController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\Identity\\UsersController.cs", "RelativeToolTip": "Web.Host\\Controllers\\Identity\\UsersController.cs", "ViewState": "AgIAAPEAAAAAAAAAAAAuwPwAAACBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T13:09:59.395Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "Metadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Metadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\Metadata.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Metadata.cs", "RelativeToolTip": "Domain\\Entities\\Metadata.cs", "ViewState": "AgIAAK0AAAAAAAAAAAAqwNAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T07:24:54.923Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "BulkCreateUsersCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Identity\\Commands\\BulkCreateUsersCommandHandler.cs", "RelativeDocumentMoniker": "Application\\Identity\\Commands\\BulkCreateUsersCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Identity\\Commands\\BulkCreateUsersCommandHandler.cs", "RelativeToolTip": "Application\\Identity\\Commands\\BulkCreateUsersCommandHandler.cs", "ViewState": "AgIAAF0AAAAAAAAAAAAYwHkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T13:10:18.118Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "BulkUpdateUserRolesCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Identity\\Commands\\BulkUpdateUserRolesCommandHandler.cs", "RelativeDocumentMoniker": "Application\\Identity\\Commands\\BulkUpdateUserRolesCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Identity\\Commands\\BulkUpdateUserRolesCommandHandler.cs", "RelativeToolTip": "Application\\Identity\\Commands\\BulkUpdateUserRolesCommandHandler.cs", "ViewState": "AgIAAJkAAAAAAAAAAAAYwLwAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T13:11:45.627Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "ContextSpecifications.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Context\\Specifications\\ContextSpecifications.cs", "RelativeDocumentMoniker": "Application\\Context\\Specifications\\ContextSpecifications.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Context\\Specifications\\ContextSpecifications.cs", "RelativeToolTip": "Application\\Context\\Specifications\\ContextSpecifications.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACcAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T10:31:41.75Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "complete-real-estate-with-values.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-real-estate-with-values.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-real-estate-with-values.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-real-estate-with-values.json", "RelativeToolTip": "Shared\\Common\\complete-real-estate-with-values.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:33.635Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "complete-apartment-values-requests.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-apartment-values-requests.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-apartment-values-requests.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-apartment-values-requests.json", "RelativeToolTip": "Shared\\Common\\complete-apartment-values-requests.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:31.078Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "SubscriptionsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\SubscriptionsController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\SubscriptionsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\SubscriptionsController.cs", "RelativeToolTip": "Web.Host\\Controllers\\SubscriptionsController.cs", "ViewState": "AgIAABUAAAAAAAAAAAAQwB8AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T18:51:15.335Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "ContextController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ContextController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ContextController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ContextController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ContextController.cs", "ViewState": "AgIAANkAAAAAAAAAAADwv/4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T10:08:29.181Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "sample-bulk-payload.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\sample-bulk-payload.json", "RelativeDocumentMoniker": "Shared\\Common\\sample-bulk-payload.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\sample-bulk-payload.json", "RelativeToolTip": "Shared\\Common\\sample-bulk-payload.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-19T18:09:47.708Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "sample-object-payloads.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\sample-object-payloads.json", "RelativeDocumentMoniker": "Shared\\Common\\sample-object-payloads.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\sample-object-payloads.json", "RelativeToolTip": "Shared\\Common\\sample-object-payloads.json", "ViewState": "AgIAACoAAAAAAAAAAAAAAD4AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-19T18:09:48.485Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "appartmentinventory.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\appartmentinventory.json", "RelativeDocumentMoniker": "Shared\\Common\\appartmentinventory.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\appartmentinventory.json", "RelativeToolTip": "Shared\\Common\\appartmentinventory.json", "ViewState": "AgIAAE8aAAAAAAAAAAAUwGsaAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T12:57:42.824Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "complete-apartment-values-guide.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-apartment-values-guide.md", "RelativeDocumentMoniker": "Shared\\Common\\complete-apartment-values-guide.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-apartment-values-guide.md", "RelativeToolTip": "Shared\\Common\\complete-apartment-values-guide.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-11T13:48:51.445Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "complete-structured-hierarchy.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-structured-hierarchy.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-structured-hierarchy.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-structured-hierarchy.json", "RelativeToolTip": "Shared\\Common\\complete-structured-hierarchy.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:35.196Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "complete-with-values.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-with-values.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-with-values.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-with-values.json", "RelativeToolTip": "Shared\\Common\\complete-with-values.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:36.491Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "datatype.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\datatype.json", "RelativeDocumentMoniker": "Shared\\Common\\datatype.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\datatype.json", "RelativeToolTip": "Shared\\Common\\datatype.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:37.666Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "complete-apartment-values-all-objects.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-apartment-values-all-objects.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-apartment-values-all-objects.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-apartment-values-all-objects.json", "RelativeToolTip": "Shared\\Common\\complete-apartment-values-all-objects.json", "ViewState": "AgIAACEAAAAAAAAAAAAAACAAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T12:59:41.453Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "complete-all-objects-values.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-all-objects-values.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-all-objects-values.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-all-objects-values.json", "RelativeToolTip": "Shared\\Common\\complete-all-objects-values.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:27.814Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "complete-all-13-objects-values.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-all-13-objects-values.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-all-13-objects-values.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-all-13-objects-values.json", "RelativeToolTip": "Shared\\Common\\complete-all-13-objects-values.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-19T18:09:29.852Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "apartment-bulk-upsert-requests.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-bulk-upsert-requests.json", "RelativeDocumentMoniker": "Shared\\Common\\apartment-bulk-upsert-requests.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-bulk-upsert-requests.json", "RelativeToolTip": "Shared\\Common\\apartment-bulk-upsert-requests.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-19T18:09:26.567Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "apartment_3d_structure_guide.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment_3d_structure_guide.md", "RelativeDocumentMoniker": "Shared\\Common\\apartment_3d_structure_guide.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment_3d_structure_guide.md", "RelativeToolTip": "Shared\\Common\\apartment_3d_structure_guide.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-19T18:09:24.949Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "all-objects-metadata-mapping.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\all-objects-metadata-mapping.json", "RelativeDocumentMoniker": "Shared\\Common\\all-objects-metadata-mapping.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\all-objects-metadata-mapping.json", "RelativeToolTip": "Shared\\Common\\all-objects-metadata-mapping.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:05.185Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "DapperRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\DapperRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Repositories\\DapperRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\DapperRepository.cs", "RelativeToolTip": "Infrastructure\\Database\\Repositories\\DapperRepository.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAkwGYAAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T10:23:54.192Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "apartment-sample-data-readme.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-sample-data-readme.md", "RelativeDocumentMoniker": "Shared\\Common\\apartment-sample-data-readme.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-sample-data-readme.md", "RelativeToolTip": "Shared\\Common\\apartment-sample-data-readme.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-16T08:44:21.437Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "GetObjectInstanceViewQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\Queries\\GetObjectInstanceView\\GetObjectInstanceViewQueryHandler.cs", "RelativeDocumentMoniker": "Application\\ObjectValues\\Queries\\GetObjectInstanceView\\GetObjectInstanceViewQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\Queries\\GetObjectInstanceView\\GetObjectInstanceViewQueryHandler.cs", "RelativeToolTip": "Application\\ObjectValues\\Queries\\GetObjectInstanceView\\GetObjectInstanceViewQueryHandler.cs", "ViewState": "AgIAABkAAAAAAAAAAAAQwCYAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T18:02:57.637Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "ObjectsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ObjectsController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ObjectsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ObjectsController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ObjectsController.cs", "ViewState": "AgIAADUAAAAAAAAAAAAmwH8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:45:14.069Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "apartment_3d_template.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment_3d_template.json", "RelativeDocumentMoniker": "Shared\\Common\\apartment_3d_template.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment_3d_template.json", "RelativeToolTip": "Shared\\Common\\apartment_3d_template.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:09.237Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "database.dev.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.dev.json", "RelativeDocumentMoniker": "Web.Host\\Configurations\\database.dev.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.dev.json", "RelativeToolTip": "Web.Host\\Configurations\\database.dev.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-09T06:18:35.24Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "apartment_3d_complex_example.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment_3d_complex_example.json", "RelativeDocumentMoniker": "Shared\\Common\\apartment_3d_complex_example.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment_3d_complex_example.json", "RelativeToolTip": "Shared\\Common\\apartment_3d_complex_example.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:07.498Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "ComprehensiveEntityDataDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ComprehensiveEntityData\\DTOs\\ComprehensiveEntityDataDto.cs", "RelativeDocumentMoniker": "Application\\ComprehensiveEntityData\\DTOs\\ComprehensiveEntityDataDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ComprehensiveEntityData\\DTOs\\ComprehensiveEntityDataDto.cs", "RelativeToolTip": "Application\\ComprehensiveEntityData\\DTOs\\ComprehensiveEntityDataDto.cs", "ViewState": "AgIAAAsAAAAAAAAAAIA/wB4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T11:50:03.295Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "HierarchicalEntityDataQueries.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\HierarchicalEntityDataQueries.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Repositories\\HierarchicalEntityDataQueries.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\HierarchicalEntityDataQueries.cs", "RelativeToolTip": "Infrastructure\\Database\\Repositories\\HierarchicalEntityDataQueries.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T12:09:09.993Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 48, "Title": "GetHierarchicalEntityDataQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ComprehensiveEntityData\\Queries\\GetHierarchicalEntityDataQueryHandler.cs", "RelativeDocumentMoniker": "Application\\ComprehensiveEntityData\\Queries\\GetHierarchicalEntityDataQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ComprehensiveEntityData\\Queries\\GetHierarchicalEntityDataQueryHandler.cs", "RelativeToolTip": "Application\\ComprehensiveEntityData\\Queries\\GetHierarchicalEntityDataQueryHandler.cs", "ViewState": "AgIAABYAAAAAAAAAAAAQwCcAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T12:06:54.334Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "TemplateConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\TemplateConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\TemplateConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\TemplateConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\TemplateConfig.cs", "ViewState": "AgIAABMAAAAAAAAAAAAQwCAAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T10:43:10.028Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "database.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.json", "RelativeDocumentMoniker": "Web.Host\\Configurations\\database.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.json", "RelativeToolTip": "Web.Host\\Configurations\\database.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T06:00:33.757Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "database.prd.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.prd.json", "RelativeDocumentMoniker": "Web.Host\\Configurations\\database.prd.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.prd.json", "RelativeToolTip": "Web.Host\\Configurations\\database.prd.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAChAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T06:00:28.036Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "database.qa.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.qa.json", "RelativeDocumentMoniker": "Web.Host\\Configurations\\database.qa.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Configurations\\database.qa.json", "RelativeToolTip": "Web.Host\\Configurations\\database.qa.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T06:00:30.893Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "ApplicationDbContextModelSnapshot.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Migrators\\Migrations\\Application\\ApplicationDbContextModelSnapshot.cs", "RelativeDocumentMoniker": "Migrators\\Migrations\\Application\\ApplicationDbContextModelSnapshot.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Migrators\\Migrations\\Application\\ApplicationDbContextModelSnapshot.cs", "RelativeToolTip": "Migrators\\Migrations\\Application\\ApplicationDbContextModelSnapshot.cs", "ViewState": "AgIAANYAAAAAAAAAAAAQwO0AAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T15:22:59.244Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 55, "Title": "SubscriptionConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\SubscriptionConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\SubscriptionConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\SubscriptionConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\SubscriptionConfig.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAQwCkAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T10:42:54.287Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "Subscription.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Subscription.cs", "RelativeDocumentMoniker": "Domain\\Entities\\Subscription.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Subscription.cs", "RelativeToolTip": "Domain\\Entities\\Subscription.cs", "ViewState": "AgIAACgAAAAAAAAAAAAkwDoAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:32:21.122Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "DataType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\DataType.cs", "RelativeDocumentMoniker": "Domain\\Entities\\DataType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\DataType.cs", "RelativeToolTip": "Domain\\Entities\\DataType.cs", "ViewState": "AgIAADwAAAAAAAAAAAAcwFkAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:12:25.389Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "ProductValueConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ProductValueConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\ProductValueConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ProductValueConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\ProductValueConfig.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAiwAkAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T10:51:39.536Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "CreateProductWithSubscriptionCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Products\\Commands\\CreateProductWithSubscriptionCommandHandler.cs", "RelativeDocumentMoniker": "Application\\Products\\Commands\\CreateProductWithSubscriptionCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Products\\Commands\\CreateProductWithSubscriptionCommandHandler.cs", "RelativeToolTip": "Application\\Products\\Commands\\CreateProductWithSubscriptionCommandHandler.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAkwDkAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T16:03:24.792Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "TemplatesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\TemplatesController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\TemplatesController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\TemplatesController.cs", "RelativeToolTip": "Web.Host\\Controllers\\TemplatesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T16:13:07.149Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "DataTypeByNameSpec.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Specifications\\DataTypeByNameSpec.cs", "RelativeDocumentMoniker": "Application\\DataTypes\\Specifications\\DataTypeByNameSpec.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Specifications\\DataTypeByNameSpec.cs", "RelativeToolTip": "Application\\DataTypes\\Specifications\\DataTypeByNameSpec.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T13:50:25.026Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "GetDataTypesQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Queries\\GetDataTypesQuery.cs", "RelativeDocumentMoniker": "Application\\DataTypes\\Queries\\GetDataTypesQuery.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Queries\\GetDataTypesQuery.cs", "RelativeToolTip": "Application\\DataTypes\\Queries\\GetDataTypesQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T13:50:29.894Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Product.cs", "RelativeDocumentMoniker": "Domain\\Entities\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Product.cs", "RelativeToolTip": "Domain\\Entities\\Product.cs", "ViewState": "AgIAACkAAAAAAAAAAAAswE8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:29:20.424Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "ProductsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ProductsController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ProductsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ProductsController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ProductsController.cs", "ViewState": "AgIAACQAAAAAAAAAAAAYwD0AAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:45:40.13Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "GetDataTypeByIdQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Queries\\GetDataTypeByIdQueryHandler.cs", "RelativeDocumentMoniker": "Application\\DataTypes\\Queries\\GetDataTypeByIdQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Queries\\GetDataTypeByIdQueryHandler.cs", "RelativeToolTip": "Application\\DataTypes\\Queries\\GetDataTypeByIdQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T13:50:30.323Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 62, "Title": "RepositoryBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\RepositoryBase.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Repositories\\RepositoryBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\RepositoryBase.cs", "RelativeToolTip": "Infrastructure\\Database\\Repositories\\RepositoryBase.cs", "ViewState": "AgIAADIAAAAAAAAAAIA5wPYAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T14:16:38.298Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "ContextController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Api.Host\\Controllers\\ContextController.cs", "RelativeDocumentMoniker": "Api.Host\\Controllers\\ContextController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Api.Host\\Controllers\\ContextController.cs", "RelativeToolTip": "Api.Host\\Controllers\\ContextController.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAqwGYBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T14:01:03.898Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "HierarchicalEntityDataMappingDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\HierarchicalEntityDataMappingDtos.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Repositories\\HierarchicalEntityDataMappingDtos.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Repositories\\HierarchicalEntityDataMappingDtos.cs", "RelativeToolTip": "Infrastructure\\Database\\Repositories\\HierarchicalEntityDataMappingDtos.cs", "ViewState": "AgIAAM4AAAAAAAAAAAAowNoAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T05:19:07.315Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 154, "Title": "ComprehensiveEntityDataController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ComprehensiveEntityDataController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ComprehensiveEntityDataController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\ComprehensiveEntityDataController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ComprehensiveEntityDataController.cs", "ViewState": "AgIAAMMAAAAAAAAAAAAYwMoAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T08:22:32.191Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "CreateDataTypeCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Commands\\CreateDataTypeCommandHandler.cs", "RelativeDocumentMoniker": "Application\\DataTypes\\Commands\\CreateDataTypeCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\DataTypes\\Commands\\CreateDataTypeCommandHandler.cs", "RelativeToolTip": "Application\\DataTypes\\Commands\\CreateDataTypeCommandHandler.cs", "ViewState": "AgIAACkAAAAAAAAAAIAwwDgAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:23:08.536Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "MetadataDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\MetadataManagement\\DTOs\\MetadataDto.cs", "RelativeDocumentMoniker": "Application\\MetadataManagement\\DTOs\\MetadataDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\MetadataManagement\\DTOs\\MetadataDto.cs", "RelativeToolTip": "Application\\MetadataManagement\\DTOs\\MetadataDto.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAxwBoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:18:10.234Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "complete-hierarchy-request.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-hierarchy-request.json", "RelativeDocumentMoniker": "Shared\\Common\\complete-hierarchy-request.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\complete-hierarchy-request.json", "RelativeToolTip": "Shared\\Common\\complete-hierarchy-request.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:32.369Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "apartment-values-sample-data.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-values-sample-data.json", "RelativeDocumentMoniker": "Shared\\Common\\apartment-values-sample-data.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-values-sample-data.json", "RelativeToolTip": "Shared\\Common\\apartment-values-sample-data.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:22.682Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "apartment-sample-data.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-sample-data.json", "RelativeDocumentMoniker": "Shared\\Common\\apartment-sample-data.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-sample-data.json", "RelativeToolTip": "Shared\\Common\\apartment-sample-data.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T08:44:12.63Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "apartment-api-usage-guide.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-api-usage-guide.md", "RelativeDocumentMoniker": "Shared\\Common\\apartment-api-usage-guide.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\apartment-api-usage-guide.md", "RelativeToolTip": "Shared\\Common\\apartment-api-usage-guide.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-16T08:44:10.227Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "MetadataConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\MetadataConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\MetadataConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\MetadataConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\MetadataConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:09:50.282Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 78, "Title": "ObjectValuesRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Repositories\\ObjectValuesRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\Repositories\\ObjectValuesRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Repositories\\ObjectValuesRepository.cs", "RelativeToolTip": "Infrastructure\\Repositories\\ObjectValuesRepository.cs", "ViewState": "AgIAAO0CAAAAAAAAAAAMwAwDAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:22:42.392Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "ObjectValuesDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\DTOs\\ObjectValuesDto.cs", "RelativeDocumentMoniker": "Application\\ObjectValues\\DTOs\\ObjectValuesDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\DTOs\\ObjectValuesDto.cs", "RelativeToolTip": "Application\\ObjectValues\\DTOs\\ObjectValuesDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGwAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T05:23:48.546Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "IRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Abstraction\\Database\\Repositories\\IRepository.cs", "RelativeDocumentMoniker": "Abstraction\\Database\\Repositories\\IRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Abstraction\\Database\\Repositories\\IRepository.cs", "RelativeToolTip": "Abstraction\\Database\\Repositories\\IRepository.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAgwCEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T05:32:05.494Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "DynamicOperationDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Abstraction\\Common\\DynamicOperationDto.cs", "RelativeDocumentMoniker": "Abstraction\\Common\\DynamicOperationDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Abstraction\\Common\\DynamicOperationDto.cs", "RelativeToolTip": "Abstraction\\Common\\DynamicOperationDto.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAuwD8AAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T05:31:39.988Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "Startup.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Startup.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Startup.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Startup.cs", "RelativeToolTip": "Infrastructure\\Database\\Startup.cs", "ViewState": "AgIAADoAAAAAAAAAAAAewEUAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:55:12.611Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "HierarchicalEntityDataService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Services\\HierarchicalEntityDataService.cs", "RelativeDocumentMoniker": "Infrastructure\\Services\\HierarchicalEntityDataService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Services\\HierarchicalEntityDataService.cs", "RelativeToolTip": "Infrastructure\\Services\\HierarchicalEntityDataService.cs", "ViewState": "AgIAABEAAAAAAAAAAAAAwCkAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:21:28.863Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "GetTenantWithProductQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\MultiTenancy\\Queries\\GetTenantWithProductQueryHandler.cs", "RelativeDocumentMoniker": "Application\\MultiTenancy\\Queries\\GetTenantWithProductQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\MultiTenancy\\Queries\\GetTenantWithProductQueryHandler.cs", "RelativeToolTip": "Application\\MultiTenancy\\Queries\\GetTenantWithProductQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:21:16.319Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "GetTenantWithProductQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\MultiTenancy\\Queries\\GetTenantWithProductQuery.cs", "RelativeDocumentMoniker": "Application\\MultiTenancy\\Queries\\GetTenantWithProductQuery.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\MultiTenancy\\Queries\\GetTenantWithProductQuery.cs", "RelativeToolTip": "Application\\MultiTenancy\\Queries\\GetTenantWithProductQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:21:15.274Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "AssemblyReference.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\AssemblyReference.cs", "RelativeDocumentMoniker": "Application\\AssemblyReference.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\AssemblyReference.cs", "RelativeToolTip": "Application\\AssemblyReference.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:21:00.679Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "Startup.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Startup.cs", "RelativeDocumentMoniker": "Infrastructure\\Startup.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Startup.cs", "RelativeToolTip": "Infrastructure\\Startup.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABgAAABbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:21:05.955Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "Class1.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Class1.cs", "RelativeDocumentMoniker": "Application\\Class1.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Class1.cs", "RelativeToolTip": "Application\\Class1.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:20:59.919Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "UserMetadataByUserAndMetadataSpec.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\UserMetadataManagement\\Specifications\\UserMetadataByUserAndMetadataSpec.cs", "RelativeDocumentMoniker": "Application\\UserMetadataManagement\\Specifications\\UserMetadataByUserAndMetadataSpec.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\UserMetadataManagement\\Specifications\\UserMetadataByUserAndMetadataSpec.cs", "RelativeToolTip": "Application\\UserMetadataManagement\\Specifications\\UserMetadataByUserAndMetadataSpec.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T17:20:51.067Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "UpsertObjectWithMetadataCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommandHandler.cs", "RelativeDocumentMoniker": "Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommandHandler.cs", "RelativeToolTip": "Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommandHandler.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:54:46.708Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "UpsertObjectWithMetadataBulkCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataBulkCommandHandler.cs", "RelativeDocumentMoniker": "Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataBulkCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataBulkCommandHandler.cs", "RelativeToolTip": "Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataBulkCommandHandler.cs", "ViewState": "AgIAAGUAAAAAAAAAAADwv3YAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T10:03:19.757Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "Object.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Object.cs", "RelativeDocumentMoniker": "Domain\\Entities\\Object.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Object.cs", "RelativeToolTip": "Domain\\Entities\\Object.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:21:42.339Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "AuditableEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\AuditableEntity.cs", "RelativeDocumentMoniker": "Domain\\Common\\Contracts\\AuditableEntity.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\AuditableEntity.cs", "RelativeToolTip": "Domain\\Common\\Contracts\\AuditableEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAUAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:09:46.91Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "BaseEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\BaseEntity.cs", "RelativeDocumentMoniker": "Domain\\Common\\Contracts\\BaseEntity.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\BaseEntity.cs", "RelativeToolTip": "Domain\\Common\\Contracts\\BaseEntity.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAAwCEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:10:00.905Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "UpsertObjectWithMetadataCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommand.cs", "RelativeDocumentMoniker": "Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommand.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommand.cs", "RelativeToolTip": "Application\\Objects\\Commands\\UpsertObjectWithMetadata\\UpsertObjectWithMetadataCommand.cs", "ViewState": "AgIAAB0AAAAAAAAAAAA3wDEAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T16:13:20.62Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "UpsertBulkObjectValueCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\Commands\\UpsertBulk\\UpsertBulkObjectValueCommandHandler.cs", "RelativeDocumentMoniker": "Application\\ObjectValues\\Commands\\UpsertBulk\\UpsertBulkObjectValueCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\Commands\\UpsertBulk\\UpsertBulkObjectValueCommandHandler.cs", "RelativeToolTip": "Application\\ObjectValues\\Commands\\UpsertBulk\\UpsertBulkObjectValueCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:54:57.863Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "ObjectByFeatureAndNameSpec.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Specifications\\ObjectByFeatureAndNameSpec.cs", "RelativeDocumentMoniker": "Application\\Objects\\Specifications\\ObjectByFeatureAndNameSpec.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Specifications\\ObjectByFeatureAndNameSpec.cs", "RelativeToolTip": "Application\\Objects\\Specifications\\ObjectByFeatureAndNameSpec.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:54:54.829Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "GetObjectsQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectsQueryHandler.cs", "RelativeDocumentMoniker": "Application\\Objects\\Queries\\GetObjectsQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectsQueryHandler.cs", "RelativeToolTip": "Application\\Objects\\Queries\\GetObjectsQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:54:52.887Z"}, {"$type": "Document", "DocumentIndex": 96, "Title": "GetObjectByIdWithMetadataQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectByIdWithMetadataQueryHandler.cs", "RelativeDocumentMoniker": "Application\\Objects\\Queries\\GetObjectByIdWithMetadataQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectByIdWithMetadataQueryHandler.cs", "RelativeToolTip": "Application\\Objects\\Queries\\GetObjectByIdWithMetadataQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:54:52.059Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "GetObjectByIdQueryHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectByIdQueryHandler.cs", "RelativeDocumentMoniker": "Application\\Objects\\Queries\\GetObjectByIdQueryHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Objects\\Queries\\GetObjectByIdQueryHandler.cs", "RelativeToolTip": "Application\\Objects\\Queries\\GetObjectByIdQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:54:48.474Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "DynamicValueOperationCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Common\\Commands\\DynamicValueOperationCommandHandler.cs", "RelativeDocumentMoniker": "Application\\Common\\Commands\\DynamicValueOperationCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\Common\\Commands\\DynamicValueOperationCommandHandler.cs", "RelativeToolTip": "Application\\Common\\Commands\\DynamicValueOperationCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T10:54:45.095Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "MetadataController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\MetadataController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\MetadataController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\MetadataController.cs", "RelativeToolTip": "Web.Host\\Controllers\\MetadataController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:44:06.363Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "IntegrationsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\IntegrationsController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\IntegrationsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\IntegrationsController.cs", "RelativeToolTip": "Web.Host\\Controllers\\IntegrationsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:44:04.7Z"}, {"$type": "Document", "DocumentIndex": 101, "Title": "IntegrationConfigurationsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\IntegrationConfigurationsController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\IntegrationConfigurationsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\IntegrationConfigurationsController.cs", "RelativeToolTip": "Web.Host\\Controllers\\IntegrationConfigurationsController.cs", "ViewState": "AgIAABYAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:44:01.897Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "IntegrationApisController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\IntegrationApisController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\IntegrationApisController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\IntegrationApisController.cs", "RelativeToolTip": "Web.Host\\Controllers\\IntegrationApisController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:43:59.669Z"}, {"$type": "Document", "DocumentIndex": 103, "Title": "FieldMappingsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\FieldMappingsController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\FieldMappingsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\FieldMappingsController.cs", "RelativeToolTip": "Web.Host\\Controllers\\FieldMappingsController.cs", "ViewState": "AgIAACEAAAAAAAAAAAAYwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:43:55.657Z"}, {"$type": "Document", "DocumentIndex": 105, "Title": "DataTransformationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\DataTransformationController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\DataTransformationController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\DataTransformationController.cs", "RelativeToolTip": "Web.Host\\Controllers\\DataTransformationController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:43:13.525Z"}, {"$type": "Document", "DocumentIndex": 107, "Title": "ISoftDelete.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\ISoftDelete.cs", "RelativeDocumentMoniker": "Domain\\Common\\Contracts\\ISoftDelete.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\ISoftDelete.cs", "RelativeToolTip": "Domain\\Common\\Contracts\\ISoftDelete.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:41:38.304Z"}, {"$type": "Document", "DocumentIndex": 106, "Title": "IEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\IEntity.cs", "RelativeDocumentMoniker": "Domain\\Common\\Contracts\\IEntity.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\IEntity.cs", "RelativeToolTip": "Domain\\Common\\Contracts\\IEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:41:28.193Z"}, {"$type": "Document", "DocumentIndex": 104, "Title": "DataTypesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\DataTypesController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\DataTypesController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\DataTypesController.cs", "RelativeToolTip": "Web.Host\\Controllers\\DataTypesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:08:40.157Z"}, {"$type": "Document", "DocumentIndex": 108, "Title": "IAuditableEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\IAuditableEntity.cs", "RelativeDocumentMoniker": "Domain\\Common\\Contracts\\IAuditableEntity.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\IAuditableEntity.cs", "RelativeToolTip": "Domain\\Common\\Contracts\\IAuditableEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:41:22.963Z"}, {"$type": "Document", "DocumentIndex": 109, "Title": "IAggregateRoot.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\IAggregateRoot.cs", "RelativeDocumentMoniker": "Domain\\Common\\Contracts\\IAggregateRoot.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\IAggregateRoot.cs", "RelativeToolTip": "Domain\\Common\\Contracts\\IAggregateRoot.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:41:22.226Z"}, {"$type": "Document", "DocumentIndex": 110, "Title": "DomainEvent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\DomainEvent.cs", "RelativeDocumentMoniker": "Domain\\Common\\Contracts\\DomainEvent.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Common\\Contracts\\DomainEvent.cs", "RelativeToolTip": "Domain\\Common\\Contracts\\DomainEvent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:41:20.117Z"}, {"$type": "Document", "DocumentIndex": 111, "Title": "JwtSettings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Identity\\JwtSettings.cs", "RelativeDocumentMoniker": "Domain\\Identity\\JwtSettings.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Identity\\JwtSettings.cs", "RelativeToolTip": "Domain\\Identity\\JwtSettings.cs", "ViewState": "AgIAAA8AAAAAAAAAAAA1wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:41:07.389Z"}, {"$type": "Document", "DocumentIndex": 115, "Title": "User.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\User.cs", "RelativeDocumentMoniker": "Domain\\Entities\\User.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\User.cs", "RelativeToolTip": "Domain\\Entities\\User.cs", "ViewState": "AgIAAEUAAAAAAAAAAAA4wBkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:29:37.832Z"}, {"$type": "Document", "DocumentIndex": 116, "Title": "TenantInfoValue.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\TenantInfoValue.cs", "RelativeDocumentMoniker": "Domain\\Entities\\TenantInfoValue.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\TenantInfoValue.cs", "RelativeToolTip": "Domain\\Entities\\TenantInfoValue.cs", "ViewState": "AgIAABoAAAAAAAAAAAAgwBcAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T07:24:08.012Z"}, {"$type": "Document", "DocumentIndex": 117, "Title": "TenantInfoMetadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\TenantInfoMetadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\TenantInfoMetadata.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\TenantInfoMetadata.cs", "RelativeToolTip": "Domain\\Entities\\TenantInfoMetadata.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAAkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T07:24:19.92Z"}, {"$type": "Document", "DocumentIndex": 118, "Title": "AppTenantInfo.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\MultiTenancy\\AppTenantInfo.cs", "RelativeDocumentMoniker": "Domain\\MultiTenancy\\AppTenantInfo.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\MultiTenancy\\AppTenantInfo.cs", "RelativeToolTip": "Domain\\MultiTenancy\\AppTenantInfo.cs", "ViewState": "AgIAAFsAAAAAAAAAAAA8wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:40:11.605Z"}, {"$type": "Document", "DocumentIndex": 114, "Title": "UserMetadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\UserMetadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\UserMetadata.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\UserMetadata.cs", "RelativeToolTip": "Domain\\Entities\\UserMetadata.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADUAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:34:38.588Z"}, {"$type": "Document", "DocumentIndex": 113, "Title": "UserValue.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\UserValue.cs", "RelativeDocumentMoniker": "Domain\\Entities\\UserValue.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\UserValue.cs", "RelativeToolTip": "Domain\\Entities\\UserValue.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:35:09.383Z"}, {"$type": "Document", "DocumentIndex": 112, "Title": "UserRole.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\UserRole.cs", "RelativeDocumentMoniker": "Domain\\Entities\\UserRole.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\UserRole.cs", "RelativeToolTip": "Domain\\Entities\\UserRole.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:19:55.76Z"}, {"$type": "Document", "DocumentIndex": 119, "Title": "SyncHistory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\SyncHistory.cs", "RelativeDocumentMoniker": "Domain\\Entities\\SyncHistory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\SyncHistory.cs", "RelativeToolTip": "Domain\\Entities\\SyncHistory.cs", "ViewState": "AgIAAD4AAAAAAAAAAAAUwBEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:33:36.781Z"}, {"$type": "Document", "DocumentIndex": 120, "Title": "SubscriptionValue.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\SubscriptionValue.cs", "RelativeDocumentMoniker": "Domain\\Entities\\SubscriptionValue.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\SubscriptionValue.cs", "RelativeToolTip": "Domain\\Entities\\SubscriptionValue.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:33:29.855Z"}, {"$type": "Document", "DocumentIndex": 121, "Title": "SubscriptionMetadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\SubscriptionMetadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\SubscriptionMetadata.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\SubscriptionMetadata.cs", "RelativeToolTip": "Domain\\Entities\\SubscriptionMetadata.cs", "ViewState": "AgIAACEAAAAAAAAAAAAkwB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:32:58.335Z"}, {"$type": "Document", "DocumentIndex": 122, "Title": "RoleValue.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\RoleValue.cs", "RelativeDocumentMoniker": "Domain\\Entities\\RoleValue.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\RoleValue.cs", "RelativeToolTip": "Domain\\Entities\\RoleValue.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:32:15.475Z"}, {"$type": "Document", "DocumentIndex": 123, "Title": "RoleMetadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\RoleMetadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\RoleMetadata.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\RoleMetadata.cs", "RelativeToolTip": "Domain\\Entities\\RoleMetadata.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAQwDEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:31:49.763Z"}, {"$type": "Document", "DocumentIndex": 124, "Title": "Role.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Role.cs", "RelativeDocumentMoniker": "Domain\\Entities\\Role.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Role.cs", "RelativeToolTip": "Domain\\Entities\\Role.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:31:34.509Z"}, {"$type": "Document", "DocumentIndex": 125, "Title": "ProductValue.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ProductValue.cs", "RelativeDocumentMoniker": "Domain\\Entities\\ProductValue.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ProductValue.cs", "RelativeToolTip": "Domain\\Entities\\ProductValue.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAAwBwAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:31:16.946Z"}, {"$type": "Document", "DocumentIndex": 126, "Title": "ProductMetadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ProductMetadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\ProductMetadata.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ProductMetadata.cs", "RelativeToolTip": "Domain\\Entities\\ProductMetadata.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAwwC4AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:30:41.86Z"}, {"$type": "Document", "DocumentIndex": 127, "Title": "ObjectValue.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ObjectValue.cs", "RelativeDocumentMoniker": "Domain\\Entities\\ObjectValue.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ObjectValue.cs", "RelativeToolTip": "Domain\\Entities\\ObjectValue.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T07:26:14.856Z"}, {"$type": "Document", "DocumentIndex": 128, "Title": "ObjectMetadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ObjectMetadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\ObjectMetadata.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ObjectMetadata.cs", "RelativeToolTip": "Domain\\Entities\\ObjectMetadata.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:28:12.987Z"}, {"$type": "Document", "DocumentIndex": 129, "Title": "IntegrationConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\IntegrationConfiguration.cs", "RelativeDocumentMoniker": "Domain\\Entities\\IntegrationConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\IntegrationConfiguration.cs", "RelativeToolTip": "Domain\\Entities\\IntegrationConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:20:09.748Z"}, {"$type": "Document", "DocumentIndex": 130, "Title": "IntegrationApi.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\IntegrationApi.cs", "RelativeDocumentMoniker": "Domain\\Entities\\IntegrationApi.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\IntegrationApi.cs", "RelativeToolTip": "Domain\\Entities\\IntegrationApi.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAkwA8AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:19:59.19Z"}, {"$type": "Document", "DocumentIndex": 131, "Title": "Integration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Integration.cs", "RelativeDocumentMoniker": "Domain\\Entities\\Integration.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\Integration.cs", "RelativeToolTip": "Domain\\Entities\\Integration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:19:48.841Z"}, {"$type": "Document", "DocumentIndex": 132, "Title": "FieldMapping.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\FieldMapping.cs", "RelativeDocumentMoniker": "Domain\\Entities\\FieldMapping.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\FieldMapping.cs", "RelativeToolTip": "Domain\\Entities\\FieldMapping.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAiwDwAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:19:16.299Z"}, {"$type": "Document", "DocumentIndex": 133, "Title": "ConflictResolution.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ConflictResolution.cs", "RelativeDocumentMoniker": "Domain\\Entities\\ConflictResolution.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Domain\\Entities\\ConflictResolution.cs", "RelativeToolTip": "Domain\\Entities\\ConflictResolution.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:09:31.945Z"}, {"$type": "Document", "DocumentIndex": 134, "Title": "BaseApiController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\BaseApiController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\BaseApiController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\BaseApiController.cs", "RelativeToolTip": "Web.Host\\Controllers\\BaseApiController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T08:08:26.738Z"}, {"$type": "Document", "DocumentIndex": 135, "Title": "IDapperRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Abstraction\\Database\\Repositories\\IDapperRepository.cs", "RelativeDocumentMoniker": "Abstraction\\Database\\Repositories\\IDapperRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Abstraction\\Database\\Repositories\\IDapperRepository.cs", "RelativeToolTip": "Abstraction\\Database\\Repositories\\IDapperRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T10:34:15.741Z"}, {"$type": "Document", "DocumentIndex": 150, "Title": "correct-metadata-mapping.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\correct-metadata-mapping.json", "RelativeDocumentMoniker": "Shared\\Common\\correct-metadata-mapping.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\correct-metadata-mapping.json", "RelativeToolTip": "Shared\\Common\\correct-metadata-mapping.json", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T13:58:22.958Z"}, {"$type": "Document", "DocumentIndex": 151, "Title": "corrected-apartment-values-all-objects.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\corrected-apartment-values-all-objects.json", "RelativeDocumentMoniker": "Shared\\Common\\corrected-apartment-values-all-objects.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\corrected-apartment-values-all-objects.json", "RelativeToolTip": "Shared\\Common\\corrected-apartment-values-all-objects.json", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T13:49:03.769Z"}, {"$type": "Document", "DocumentIndex": 152, "Title": "hierarchical-relationships-summary.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\hierarchical-relationships-summary.md", "RelativeDocumentMoniker": "Shared\\Common\\hierarchical-relationships-summary.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\hierarchical-relationships-summary.md", "RelativeToolTip": "Shared\\Common\\hierarchical-relationships-summary.md", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-11T13:48:58.492Z"}, {"$type": "Document", "DocumentIndex": 153, "Title": "objectvalues-upsert-behavior.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\objectvalues-upsert-behavior.md", "RelativeDocumentMoniker": "Shared\\Common\\objectvalues-upsert-behavior.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\objectvalues-upsert-behavior.md", "RelativeToolTip": "Shared\\Common\\objectvalues-upsert-behavior.md", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-11T13:49:00.716Z"}, {"$type": "Document", "DocumentIndex": 136, "Title": "ObjectMetadataConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectMetadataConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\ObjectMetadataConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\ObjectMetadataConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\ObjectMetadataConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T13:42:58.917Z"}, {"$type": "Document", "DocumentIndex": 137, "Title": "agenttracking.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\agenttracking.json", "RelativeDocumentMoniker": "Shared\\Common\\agenttracking.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Shared\\Common\\agenttracking.json", "RelativeToolTip": "Shared\\Common\\agenttracking.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-11T12:57:41.223Z"}, {"$type": "Document", "DocumentIndex": 138, "Title": "CreateObjectValueCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\Commands\\CreateObjectValueCommandHandler.cs", "RelativeDocumentMoniker": "Application\\ObjectValues\\Commands\\CreateObjectValueCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Application\\ObjectValues\\Commands\\CreateObjectValueCommandHandler.cs", "RelativeToolTip": "Application\\ObjectValues\\Commands\\CreateObjectValueCommandHandler.cs", "ViewState": "AgIAACQAAAAAAAAAAAAjwDUAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T07:26:37.997Z"}, {"$type": "Document", "DocumentIndex": 139, "Title": "TokensController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\Identity\\TokensController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\Identity\\TokensController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Controllers\\Identity\\TokensController.cs", "RelativeToolTip": "Web.Host\\Controllers\\Identity\\TokensController.cs", "ViewState": "AgIAABkAAAAAAAAAAAAQwDAAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T07:13:47.865Z"}, {"$type": "Document", "DocumentIndex": 140, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\appsettings.json", "RelativeDocumentMoniker": "Web.Host\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\appsettings.json", "RelativeToolTip": "Web.Host\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-09T06:42:00.176Z"}, {"$type": "Document", "DocumentIndex": 142, "Title": "RoleMetadataConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\RoleMetadataConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\RoleMetadataConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\RoleMetadataConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\RoleMetadataConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T05:41:06.743Z"}, {"$type": "Document", "DocumentIndex": 141, "Title": "RoleConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\RoleConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\RoleConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\RoleConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\RoleConfig.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAABgAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:19:35.312Z"}, {"$type": "Document", "DocumentIndex": 145, "Title": "ModelBuilderExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Extensions\\ModelBuilderExtensions.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Extensions\\ModelBuilderExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Extensions\\ModelBuilderExtensions.cs", "RelativeToolTip": "Infrastructure\\Database\\Extensions\\ModelBuilderExtensions.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAQwC0AAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:12:47.679Z"}, {"$type": "Document", "DocumentIndex": 146, "Title": "UserConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\UserConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\UserConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\UserConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\UserConfig.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAQwBQAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:08:35.976Z"}, {"$type": "Document", "DocumentIndex": 143, "Title": "BaseDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\BaseDbContext.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\BaseDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\BaseDbContext.cs", "RelativeToolTip": "Infrastructure\\Database\\BaseDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:20:33.417Z"}, {"$type": "Document", "DocumentIndex": 144, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\ApplicationDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\ApplicationDbContext.cs", "RelativeToolTip": "Infrastructure\\Database\\ApplicationDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:04:40.535Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 147, "Title": "UserValueConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\UserValueConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\UserValueConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\UserValueConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\UserValueConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:10:12.99Z"}, {"$type": "Document", "DocumentIndex": 148, "Title": "FieldMappingConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\FieldMappingConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\FieldMappingConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\FieldMappingConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\FieldMappingConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:08:20.727Z"}, {"$type": "Document", "DocumentIndex": 149, "Title": "IdentityConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\IdentityConfig.cs", "RelativeDocumentMoniker": "Infrastructure\\Database\\Configuration\\Entities\\IdentityConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Database\\Configuration\\Entities\\IdentityConfig.cs", "RelativeToolTip": "Infrastructure\\Database\\Configuration\\Entities\\IdentityConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T16:36:16.838Z"}]}]}]}