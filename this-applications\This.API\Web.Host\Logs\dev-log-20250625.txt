[2025-06-25 13:25:14.903 +05:30 INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:25:15.645 +05:30 INF] Executed DbCommand (55ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-25 13:25:15.849 +05:30 INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-25 13:25:16.974 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-25 13:25:17.018 +05:30 WRN] The property 'Subscription.TemplateDetails' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-06-25 13:25:17.371 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:25:17.684 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-25 13:25:17.979 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:25:18.238 +05:30 INF] Connection to tarzen's Database Succeeded.
[2025-06-25 13:25:18.558 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:25:18.727 +05:30 INF] Connection to urban's Database Succeeded.
[2025-06-25 13:25:18.975 +05:30 INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:25:19.169 +05:30 INF] Connection to heaven's Database Succeeded.
[2025-06-25 13:25:19.446 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:25:19.598 +05:30 INF] Connection to kitchsync's Database Succeeded.
[2025-06-25 13:25:19.819 +05:30 INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:25:20.011 +05:30 INF] Connection to beta's Database Succeeded.
[2025-06-25 13:25:21.427 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-25 13:25:21.591 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-25 13:25:21.741 +05:30 INF] Now listening on: https://localhost:7222
[2025-06-25 13:25:21.745 +05:30 INF] Now listening on: http://localhost:5018
[2025-06-25 13:25:21.847 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-25 13:25:21.850 +05:30 INF] Hosting environment: dev
[2025-06-25 13:25:21.852 +05:30 INF] Content root path: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host
[2025-06-25 13:25:22.698 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-25 13:25:22.698 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_vs/browserLink - null null
[2025-06-25 13:25:22.754 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - 200 13768 application/javascript; charset=utf-8 74.4107ms
[2025-06-25 13:25:22.810 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_vs/browserLink - 200 null text/javascript; charset=UTF-8 133.6056ms
[2025-06-25 13:25:23.143 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - null null
[2025-06-25 13:25:23.744 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:25:23.765 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-25 13:25:23.773 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-25 13:25:23.777 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:25:23.780 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:25:23.783 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-25 13:25:23.785 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-25 13:25:23.976 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 833.6651ms
[2025-06-25 13:36:58.257 +05:30 INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:36:58.940 +05:30 INF] Executed DbCommand (57ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-25 13:36:59.193 +05:30 INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-25 13:37:00.198 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-25 13:37:00.234 +05:30 WRN] The property 'Subscription.TemplateDetails' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-06-25 13:37:00.589 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:00.625 +05:30 INF] Applying Migrations for 'root' tenant.
[2025-06-25 13:37:10.411 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:11.064 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-25 13:37:11.117 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-25 13:37:11.158 +05:30 INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:11.171 +05:30 INF] Applying migration '20250625080555_ObjectLookupChanges'.
[2025-06-25 13:37:11.297 +05:30 INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Genp"."ObjectLookups" ADD "TenantId" character varying(64) NOT NULL DEFAULT '';
[2025-06-25 13:37:11.387 +05:30 INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."RoleActions" (
    "Id" uuid NOT NULL,
    "RoleId" uuid NOT NULL,
    "ActionId" uuid NOT NULL,
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "TenantId" character varying(64) NOT NULL,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_RoleActions" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_RoleActions_Actions_ActionId" FOREIGN KEY ("ActionId") REFERENCES "Genp"."Actions" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_RoleActions_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Genp"."Roles" ("Id") ON DELETE CASCADE
);
[2025-06-25 13:37:11.424 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_RoleActions_ActionId" ON "Genp"."RoleActions" ("ActionId");
[2025-06-25 13:37:11.468 +05:30 INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_RoleActions_Id" ON "Genp"."RoleActions" ("Id");
[2025-06-25 13:37:11.504 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_RoleActions_IsActive" ON "Genp"."RoleActions" ("IsActive") WHERE "IsActive" = true AND "IsDeleted" = false;
[2025-06-25 13:37:11.540 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_RoleActions_RoleId" ON "Genp"."RoleActions" ("RoleId");
[2025-06-25 13:37:11.582 +05:30 INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_RoleActions_RoleId_ActionId" ON "Genp"."RoleActions" ("RoleId", "ActionId");
[2025-06-25 13:37:11.622 +05:30 INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250625080555_ObjectLookupChanges', '9.0.5');
[2025-06-25 13:37:55.981 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-25 13:37:56.220 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:56.399 +05:30 INF] Connection to tarzen's Database Succeeded.
[2025-06-25 13:37:56.696 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:56.859 +05:30 INF] Connection to urban's Database Succeeded.
[2025-06-25 13:37:57.059 +05:30 INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:57.233 +05:30 INF] Connection to heaven's Database Succeeded.
[2025-06-25 13:37:57.465 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:57.634 +05:30 INF] Connection to kitchsync's Database Succeeded.
[2025-06-25 13:37:57.868 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:37:58.032 +05:30 INF] Connection to beta's Database Succeeded.
[2025-06-25 13:37:59.247 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-25 13:37:59.405 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-25 13:37:59.543 +05:30 INF] Now listening on: https://localhost:7222
[2025-06-25 13:37:59.546 +05:30 INF] Now listening on: http://localhost:5018
[2025-06-25 13:37:59.648 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-25 13:37:59.651 +05:30 INF] Hosting environment: dev
[2025-06-25 13:37:59.653 +05:30 INF] Content root path: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host
[2025-06-25 13:38:00.422 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_vs/browserLink - null null
[2025-06-25 13:38:00.422 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-25 13:38:00.472 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - 200 13768 application/javascript; charset=utf-8 66.7856ms
[2025-06-25 13:38:00.506 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_vs/browserLink - 200 null text/javascript; charset=UTF-8 104.6818ms
[2025-06-25 13:38:00.666 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - null null
[2025-06-25 13:38:01.081 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:38:01.095 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-25 13:38:01.101 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-25 13:38:01.103 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:38:01.105 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:38:01.107 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-25 13:38:01.108 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-25 13:38:01.252 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 585.9201ms
[2025-06-25 13:39:05.387 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/api/objectvalues/object-lookup-data/c7a933ee-a0a4-4884-8b36-1709a7e3f4e0?createView=true&pageNumber=1&pageSize=10 - null null
[2025-06-25 13:39:05.426 +05:30 DBG] GetIdentifierAsync: Found identifier: "kitchsync"
[2025-06-25 13:39:05.431 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "kitchsync"
[2025-06-25 13:39:05.439 +05:30 INF] Executing endpoint 'Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host)'
[2025-06-25 13:39:05.479 +05:30 INF] Route matched with {action = "GetObjectLookupData", controller = "ObjectValues"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetObjectLookupData(System.Guid, Boolean, Int32, Int32) on controller Web.Host.Controllers.ObjectValuesController (Web.Host).
[2025-06-25 13:39:05.601 +05:30 INF] Executing action method Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host) - Validation state: "Valid"
[2025-06-25 13:39:05.612 +05:30 INF] Getting ObjectLookup data for ID: "c7a933ee-a0a4-4884-8b36-1709a7e3f4e0"
[2025-06-25 13:39:05.997 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__ef_filter__Id_0='kitchsync', @__p_0='c7a933ee-a0a4-4884-8b36-1709a7e3f4e0' (Nullable = true)], CommandType='"Text"', CommandTimeout='30']
SELECT o."Id", o."CreatedAt", o."CreatedBy", o."DisplayField", o."IsActive", o."IsDeleted", o."MetadataFieldForDisplay", o."MetadataFieldForValue", o."ModifiedAt", o."ModifiedBy", o."Name", o."ObjectId", o."SortBy", o."SortOrder", o."SourceType", o."SupportsTenantFiltering", o."TenantId", o."ValueField"
FROM "Genp"."ObjectLookups" AS o
WHERE NOT (o."IsDeleted") AND o."TenantId" = @__ef_filter__Id_0 AND o."Id" = @__p_0
LIMIT 1
[2025-06-25 13:39:06.116 +05:30 INF] Executed action method Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 504.8584ms.
[2025-06-25 13:39:06.132 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
[2025-06-25 13:39:06.147 +05:30 INF] Executed action Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host) in 658.7561ms
[2025-06-25 13:39:06.153 +05:30 INF] Executed endpoint 'Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host)'
[2025-06-25 13:39:06.161 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/api/objectvalues/object-lookup-data/c7a933ee-a0a4-4884-8b36-1709a7e3f4e0?createView=true&pageNumber=1&pageSize=10 - 400 null text/plain; charset=utf-8 774.9745ms
[2025-06-25 13:39:26.884 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/api/objectvalues/object-lookup-data/c7a933ee-a0a4-4884-8b36-1709a7e3f4e0?createView=true&pageNumber=1&pageSize=10 - null null
[2025-06-25 13:39:26.915 +05:30 DBG] GetIdentifierAsync: Found identifier: "kitchsync"
[2025-06-25 13:39:26.917 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "kitchsync"
[2025-06-25 13:39:26.920 +05:30 INF] Executing endpoint 'Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host)'
[2025-06-25 13:39:26.924 +05:30 INF] Route matched with {action = "GetObjectLookupData", controller = "ObjectValues"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetObjectLookupData(System.Guid, Boolean, Int32, Int32) on controller Web.Host.Controllers.ObjectValuesController (Web.Host).
[2025-06-25 13:39:26.956 +05:30 INF] Executing action method Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host) - Validation state: "Valid"
[2025-06-25 13:39:26.959 +05:30 INF] Getting ObjectLookup data for ID: "c7a933ee-a0a4-4884-8b36-1709a7e3f4e0"
[2025-06-25 13:39:27.215 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__ef_filter__Id_0='kitchsync', @__p_0='c7a933ee-a0a4-4884-8b36-1709a7e3f4e0' (Nullable = true)], CommandType='"Text"', CommandTimeout='30']
SELECT o."Id", o."CreatedAt", o."CreatedBy", o."DisplayField", o."IsActive", o."IsDeleted", o."MetadataFieldForDisplay", o."MetadataFieldForValue", o."ModifiedAt", o."ModifiedBy", o."Name", o."ObjectId", o."SortBy", o."SortOrder", o."SourceType", o."SupportsTenantFiltering", o."TenantId", o."ValueField"
FROM "Genp"."ObjectLookups" AS o
WHERE NOT (o."IsDeleted") AND o."TenantId" = @__ef_filter__Id_0 AND o."Id" = @__p_0
LIMIT 1
[2025-06-25 13:39:27.225 +05:30 INF] Getting metadata key values for Object: MealKit, MetadataKey: mealType, Tenant: kitchsync
[2025-06-25 13:39:27.641 +05:30 INF] Successfully retrieved 10 of 13 values for metadata key: mealType from object: MealKit (Page 1 of 2)
[2025-06-25 13:39:27.649 +05:30 INF] Executed action method Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 690.9004ms.
[2025-06-25 13:39:27.654 +05:30 INF] Executing OkObjectResult, writing value of type 'Application.ObjectValues.Queries.GetObjectLookupData.GetObjectLookupDataResponse'.
[2025-06-25 13:39:27.697 +05:30 INF] Executed action Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host) in 768.1409ms
[2025-06-25 13:39:27.701 +05:30 INF] Executed endpoint 'Web.Host.Controllers.ObjectValuesController.GetObjectLookupData (Web.Host)'
[2025-06-25 13:39:27.704 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/api/objectvalues/object-lookup-data/c7a933ee-a0a4-4884-8b36-1709a7e3f4e0?createView=true&pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 820.4211ms
[2025-06-25 13:41:35.978 +05:30 INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:41:36.454 +05:30 INF] Executed DbCommand (36ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-25 13:41:36.563 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-25 13:41:37.162 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-25 13:41:37.186 +05:30 WRN] The property 'Subscription.TemplateDetails' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-06-25 13:41:37.490 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:41:37.742 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-25 13:41:38.006 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:41:38.168 +05:30 INF] Connection to tarzen's Database Succeeded.
[2025-06-25 13:41:38.460 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:41:38.651 +05:30 INF] Connection to urban's Database Succeeded.
[2025-06-25 13:41:38.885 +05:30 INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:41:39.307 +05:30 INF] Connection to heaven's Database Succeeded.
[2025-06-25 13:41:39.520 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:41:39.691 +05:30 INF] Connection to kitchsync's Database Succeeded.
[2025-06-25 13:41:39.901 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 13:41:40.058 +05:30 INF] Connection to beta's Database Succeeded.
[2025-06-25 13:41:40.435 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-25 13:41:40.566 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-25 13:41:40.668 +05:30 INF] Now listening on: https://localhost:7222
[2025-06-25 13:41:40.669 +05:30 INF] Now listening on: http://localhost:5018
[2025-06-25 13:41:41.217 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-25 13:41:41.225 +05:30 INF] Hosting environment: dev
[2025-06-25 13:41:41.234 +05:30 INF] Content root path: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host
[2025-06-25 13:41:41.532 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-25 13:41:41.532 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_vs/browserLink - null null
[2025-06-25 13:41:41.587 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - 200 13768 application/javascript; charset=utf-8 68.8052ms
[2025-06-25 13:41:41.648 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_vs/browserLink - 200 null text/javascript; charset=UTF-8 133.077ms
[2025-06-25 13:41:41.898 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - null null
[2025-06-25 13:41:42.290 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:41:42.309 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-25 13:41:42.321 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-25 13:41:42.324 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:41:42.329 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 13:41:42.331 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-25 13:41:42.334 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-25 13:41:42.605 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 706.5742ms
[2025-06-25 16:32:25.539 +05:30 INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:32:26.320 +05:30 INF] Executed DbCommand (63ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-25 16:32:26.480 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-25 16:32:27.494 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-25 16:32:27.575 +05:30 WRN] The property 'Subscription.TemplateDetails' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-06-25 16:32:28.062 +05:30 INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:32:28.106 +05:30 INF] Applying Migrations for 'root' tenant.
[2025-06-25 16:33:21.080 +05:30 INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:21.977 +05:30 INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-25 16:33:22.039 +05:30 INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-25 16:33:22.081 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:22.103 +05:30 INF] Applying migration '20250625094113_AddSelectTypeToDataType'.
[2025-06-25 16:33:22.240 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Genp"."DataTypes" ADD "SelectType" character varying(50);
[2025-06-25 16:33:22.275 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250625094113_AddSelectTypeToDataType', '9.0.5');
[2025-06-25 16:33:24.758 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-25 16:33:25.107 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:25.331 +05:30 INF] Connection to tarzen's Database Succeeded.
[2025-06-25 16:33:25.657 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:25.883 +05:30 INF] Connection to urban's Database Succeeded.
[2025-06-25 16:33:26.175 +05:30 INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:26.356 +05:30 INF] Connection to heaven's Database Succeeded.
[2025-06-25 16:33:26.671 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:26.877 +05:30 INF] Connection to kitchsync's Database Succeeded.
[2025-06-25 16:33:27.164 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:27.393 +05:30 INF] Connection to beta's Database Succeeded.
[2025-06-25 16:33:27.684 +05:30 INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 16:33:27.886 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-25 16:33:28.888 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-25 16:33:29.040 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-25 16:33:29.159 +05:30 INF] Now listening on: https://localhost:7222
[2025-06-25 16:33:29.163 +05:30 INF] Now listening on: http://localhost:5018
[2025-06-25 16:33:29.944 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-25 16:33:29.949 +05:30 INF] Hosting environment: dev
[2025-06-25 16:33:29.953 +05:30 INF] Content root path: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host
[2025-06-25 16:33:29.966 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_vs/browserLink - null null
[2025-06-25 16:33:29.966 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-25 16:33:30.414 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - 200 13768 application/javascript; charset=utf-8 471.0989ms
[2025-06-25 16:33:30.488 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_vs/browserLink - 200 null text/javascript; charset=UTF-8 546.9862ms
[2025-06-25 16:33:31.255 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - null null
[2025-06-25 16:33:31.663 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 16:33:31.692 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-25 16:33:31.702 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-25 16:33:31.705 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 16:33:31.709 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 16:33:31.712 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-25 16:33:31.713 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-25 16:33:31.996 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 741.2527ms
[2025-06-25 18:29:01.365 +05:30 INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:01.848 +05:30 INF] Executed DbCommand (39ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-25 18:29:01.960 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-25 18:29:02.568 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-25 18:29:02.592 +05:30 WRN] The property 'Subscription.TemplateDetails' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-06-25 18:29:02.912 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:03.221 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-25 18:29:03.531 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:03.756 +05:30 INF] Connection to tarzen's Database Succeeded.
[2025-06-25 18:29:04.222 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:04.427 +05:30 INF] Connection to urban's Database Succeeded.
[2025-06-25 18:29:04.680 +05:30 INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:04.914 +05:30 INF] Connection to heaven's Database Succeeded.
[2025-06-25 18:29:05.208 +05:30 INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:05.422 +05:30 INF] Connection to kitchsync's Database Succeeded.
[2025-06-25 18:29:05.658 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:05.842 +05:30 INF] Connection to beta's Database Succeeded.
[2025-06-25 18:29:06.078 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:29:06.247 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-25 18:29:06.828 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-25 18:29:07.026 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-25 18:29:07.177 +05:30 INF] Now listening on: https://localhost:7222
[2025-06-25 18:29:07.180 +05:30 INF] Now listening on: http://localhost:5018
[2025-06-25 18:29:07.849 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-25 18:29:07.859 +05:30 INF] Hosting environment: dev
[2025-06-25 18:29:07.862 +05:30 INF] Content root path: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host
[2025-06-25 18:29:08.473 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-25 18:29:08.473 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_vs/browserLink - null null
[2025-06-25 18:29:08.562 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - 200 13768 application/javascript; charset=utf-8 108.9074ms
[2025-06-25 18:29:08.682 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_vs/browserLink - 200 null text/javascript; charset=UTF-8 228.8662ms
[2025-06-25 18:29:09.041 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - null null
[2025-06-25 18:29:09.692 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 18:29:09.711 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-25 18:29:09.721 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-25 18:29:09.724 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 18:29:09.727 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 18:29:09.730 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-25 18:29:09.731 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-25 18:29:10.189 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 1148.4092ms
[2025-06-25 18:31:22.237 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/api/objects/with-actions?ProductId=1ccca27c-7add-4907-bbca-e4ae502bc21a - null null
[2025-06-25 18:31:22.275 +05:30 DBG] GetIdentifierAsync: Found identifier: "lrbnewqa"
[2025-06-25 18:31:22.278 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "lrbnewqa"
[2025-06-25 18:31:22.285 +05:30 INF] Executing endpoint 'Web.Host.Controllers.ObjectsController.GetObjectsWithActions (Web.Host)'
[2025-06-25 18:31:22.310 +05:30 INF] Route matched with {action = "GetObjectsWithActions", controller = "Objects"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Shared.Common.Response.Result`1[System.Collections.Generic.List`1[Application.Objects.DTOs.ObjectWithActionsDto]]]] GetObjectsWithActions(Application.Objects.Queries.GetObjectsWithActionsQuery) on controller Web.Host.Controllers.ObjectsController (Web.Host).
[2025-06-25 18:31:22.439 +05:30 INF] Executing action method Web.Host.Controllers.ObjectsController.GetObjectsWithActions (Web.Host) - Validation state: "Valid"
[2025-06-25 18:31:22.806 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@__ef_filter__Id_0='lrbnewqa', @__productId_Value_0='1ccca27c-7add-4907-bbca-e4ae502bc21a', @__isActive_Value_1='True'], CommandType='"Text"', CommandTimeout='30']
SELECT o."Id", o."CreatedAt", o."CreatedBy", o."Description", o."Icon", o."IsActive", o."IsDeleted", o."ModifiedAt", o."ModifiedBy", o."Name", o."ParentObjectId", o."ProductId", o."TenantId"
FROM "Genp"."Objects" AS o
WHERE NOT (o."IsDeleted") AND o."TenantId" = @__ef_filter__Id_0 AND NOT (o."IsDeleted") AND o."ProductId" = @__productId_Value_0 AND o."IsActive" = @__isActive_Value_1
ORDER BY o."Name"
[2025-06-25 18:31:23.070 +05:30 INF] Executed DbCommand (47ms) [Parameters=[@__ef_filter__Id_1='lrbnewqa', @__ef_filter__Id_2='lrbnewqa', @__ef_filter__Id_0='lrbnewqa', @__objectIds_0={ '0b15b0a1-8f88-4fbf-9fd4-061a8e3889b1', 'c1ab320e-4d54-478c-a220-d79021e0c9b1', '0e81f424-4a85-4a9d-9757-0231539c844b', 'd9f0f4c8-ef43-4093-95e4-7232e9c92960', '9d5046ba-d1e3-4ffb-9cc5-33937e0e40fc', ... } (DbType = Object), @__isActive_Value_1='True'], CommandType='"Text"', CommandTimeout='30']
SELECT d."Id", d."AccessLevel", d."ActionId", d."CreatedAt", d."CreatedBy", d."DisplayId", d."IsActive", d."IsDefault", d."IsDeleted", d."IsVisibleInContextMenu", d."IsVisibleInRowActions", d."IsVisibleInToolbar", d."ModifiedAt", d."ModifiedBy", d."ObjectId", d."SortOrder", d."TenantId", a0."Id", a0."ButtonStyle", a0."ConfirmationMessage", a0."CreatedAt", a0."CreatedBy", a0."Description", a0."EndpointTemplate", a0."ErrorMessage", a0."Icon", a0."IsActive", a0."IsDeleted", a0."ModifiedAt", a0."ModifiedBy", a0."Name", a0."NavigationTarget", a0."SuccessMessage", a0."TenantId", d1."Id", d1."CreatedAt", d1."CreatedBy", d1."Description", d1."DisplayName", d1."Icon", d1."IsActive", d1."IsDefault", d1."IsDeleted", d1."ModifiedAt", d1."ModifiedBy", d1."Name", d1."RouteTemplate", d1."SortOrder", d1."TenantId"
FROM "Genp"."DisplayActions" AS d
INNER JOIN (
    SELECT a."Id", a."ButtonStyle", a."ConfirmationMessage", a."CreatedAt", a."CreatedBy", a."Description", a."EndpointTemplate", a."ErrorMessage", a."Icon", a."IsActive", a."IsDeleted", a."ModifiedAt", a."ModifiedBy", a."Name", a."NavigationTarget", a."SuccessMessage", a."TenantId"
    FROM "Genp"."Actions" AS a
    WHERE NOT (a."IsDeleted") AND a."TenantId" = @__ef_filter__Id_1
) AS a0 ON d."ActionId" = a0."Id"
INNER JOIN (
    SELECT d0."Id", d0."CreatedAt", d0."CreatedBy", d0."Description", d0."DisplayName", d0."Icon", d0."IsActive", d0."IsDefault", d0."IsDeleted", d0."ModifiedAt", d0."ModifiedBy", d0."Name", d0."RouteTemplate", d0."SortOrder", d0."TenantId"
    FROM "Genp"."Displays" AS d0
    WHERE NOT (d0."IsDeleted") AND d0."TenantId" = @__ef_filter__Id_2
) AS d1 ON d."DisplayId" = d1."Id"
WHERE NOT (d."IsDeleted") AND d."TenantId" = @__ef_filter__Id_0 AND d."ObjectId" = ANY (@__objectIds_0) AND NOT (d."IsDeleted") AND d."IsActive" = @__isActive_Value_1 AND a0."IsActive" AND NOT (a0."IsDeleted")
ORDER BY d."ObjectId", d."SortOrder", a0."Name"
[2025-06-25 18:31:23.206 +05:30 INF] Executed action method Web.Host.Controllers.ObjectsController.GetObjectsWithActions (Web.Host), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 762.0484ms.
[2025-06-25 18:31:23.217 +05:30 INF] Executing OkObjectResult, writing value of type 'Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.Objects.DTOs.ObjectWithActionsDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
[2025-06-25 18:31:23.273 +05:30 INF] Executed action Web.Host.Controllers.ObjectsController.GetObjectsWithActions (Web.Host) in 956.756ms
[2025-06-25 18:31:23.277 +05:30 INF] Executed endpoint 'Web.Host.Controllers.ObjectsController.GetObjectsWithActions (Web.Host)'
[2025-06-25 18:31:23.283 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/api/objects/with-actions?ProductId=1ccca27c-7add-4907-bbca-e4ae502bc21a - 200 null application/json; charset=utf-8 1045.9351ms
[2025-06-25 18:46:24.145 +05:30 INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:25.410 +05:30 INF] Executed DbCommand (111ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-25 18:46:25.731 +05:30 INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-25 18:46:27.953 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-25 18:46:28.036 +05:30 WRN] The property 'Subscription.TemplateDetails' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-06-25 18:46:28.546 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:28.904 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-25 18:46:29.233 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:29.458 +05:30 INF] Connection to tarzen's Database Succeeded.
[2025-06-25 18:46:30.100 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:30.285 +05:30 INF] Connection to urban's Database Succeeded.
[2025-06-25 18:46:30.599 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:30.806 +05:30 INF] Connection to heaven's Database Succeeded.
[2025-06-25 18:46:31.093 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:31.328 +05:30 INF] Connection to kitchsync's Database Succeeded.
[2025-06-25 18:46:31.592 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:31.803 +05:30 INF] Connection to beta's Database Succeeded.
[2025-06-25 18:46:32.133 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-25 18:46:32.374 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-25 18:46:33.237 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-25 18:46:33.490 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-25 18:46:33.702 +05:30 INF] Now listening on: https://localhost:7222
[2025-06-25 18:46:33.709 +05:30 INF] Now listening on: http://localhost:5018
[2025-06-25 18:46:34.017 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-25 18:46:34.335 +05:30 INF] Hosting environment: dev
[2025-06-25 18:46:34.963 +05:30 INF] Content root path: C:\Users\<USER>\Desktop\leadrat\this-applications\This.API\Web.Host
[2025-06-25 18:46:35.800 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_vs/browserLink - null null
[2025-06-25 18:46:35.800 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-25 18:46:35.920 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_framework/aspnetcore-browser-refresh.js - 200 13768 application/javascript; charset=utf-8 137.4464ms
[2025-06-25 18:46:35.982 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/_vs/browserLink - 200 null text/javascript; charset=UTF-8 201.6487ms
[2025-06-25 18:46:36.473 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - null null
[2025-06-25 18:46:37.094 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 18:46:37.126 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-25 18:46:37.144 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-25 18:46:37.147 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 18:46:37.151 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-25 18:46:37.153 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-25 18:46:37.155 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-25 18:46:37.530 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 1058.5294ms
[2025-06-25 18:46:54.757 +05:30 INF] Request starting HTTP/2 GET https://localhost:7222/api/roles?productId=1ccca27c-7add-4907-bbca-e4ae502bc21a - null null
[2025-06-25 18:46:54.785 +05:30 DBG] GetIdentifierAsync: Found identifier: "lrbnewqa"
[2025-06-25 18:46:54.788 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "lrbnewqa"
[2025-06-25 18:46:54.796 +05:30 INF] Executing endpoint 'Web.Host.Controllers.Identity.RolesController.GetAllAsync (Web.Host)'
[2025-06-25 18:46:54.823 +05:30 INF] Route matched with {action = "GetAll", controller = "Roles"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Shared.Common.Response.Result`1[System.Collections.Generic.List`1[Abstraction.Identity.Dtos.RoleDto]]]] GetAllAsync(System.Guid) on controller Web.Host.Controllers.Identity.RolesController (Web.Host).
[2025-06-25 18:46:54.953 +05:30 INF] Executing action method Web.Host.Controllers.Identity.RolesController.GetAllAsync (Web.Host) - Validation state: "Valid"
[2025-06-25 18:46:55.344 +05:30 INF] Executed DbCommand (37ms) [Parameters=[@__ef_filter__Id_0='lrbnewqa', @__productId_0='1ccca27c-7add-4907-bbca-e4ae502bc21a'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE r."TenantId" = @__ef_filter__Id_0 AND r."ProductId" = @__productId_0 AND NOT (r."IsDeleted")
ORDER BY r."Name"
[2025-06-25 18:46:55.503 +05:30 INF] Executed action method Web.Host.Controllers.Identity.RolesController.GetAllAsync (Web.Host), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 543.8106ms.
[2025-06-25 18:46:55.512 +05:30 INF] Executing OkObjectResult, writing value of type 'Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Abstraction.Identity.Dtos.RoleDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
[2025-06-25 18:46:55.544 +05:30 INF] Executed action Web.Host.Controllers.Identity.RolesController.GetAllAsync (Web.Host) in 712.7768ms
[2025-06-25 18:46:55.546 +05:30 INF] Executed endpoint 'Web.Host.Controllers.Identity.RolesController.GetAllAsync (Web.Host)'
[2025-06-25 18:46:55.552 +05:30 INF] Request finished HTTP/2 GET https://localhost:7222/api/roles?productId=1ccca27c-7add-4907-bbca-e4ae502bc21a - 200 null application/json; charset=utf-8 794.7457ms
