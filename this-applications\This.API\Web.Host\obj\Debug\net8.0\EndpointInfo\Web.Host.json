{"openapi": "3.0.4", "info": {"title": "API Management - Development", "description": "A comprehensive system API (Development Environment)", "contact": {"name": "<PERSON><PERSON>", "url": "https://app.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "v1"}, "paths": {"/api/comprehensive-entity": {"get": {"tags": ["ComprehensiveEntity"], "parameters": [{"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "featureId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HierarchicalEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HierarchicalEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HierarchicalEntityDataResponseDtoResult"}}}}}}}, "/api/comprehensive-entity/{productId}": {"get": {"tags": ["ComprehensiveEntity"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnifiedHierarchicalEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnifiedHierarchicalEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnifiedHierarchicalEntityDataResponseDtoResult"}}}}}}}, "/api/comprehensive-entity/subscriptions": {"get": {"tags": ["ComprehensiveEntity"], "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string"}}, {"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "subscriptionType", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "isExpired", "in": "query", "schema": {"type": "boolean"}}, {"name": "pricingTier", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "startDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "startDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "expiringWithinDays", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "orderBy", "in": "query", "schema": {"type": "string", "default": "CreatedAt"}}, {"name": "orderDirection", "in": "query", "schema": {"type": "string", "default": "desc"}}, {"name": "includeSummary", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveSubscriptionResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveSubscriptionResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveSubscriptionResponseDtoResult"}}}}}}}, "/api/comprehensive-entity/create-product-structure": {"post": {"tags": ["ComprehensiveEntity"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductStructureCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductStructureCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductStructureCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductStructureCreationResultResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductStructureCreationResultResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductStructureCreationResultResult"}}}}}}}, "/api/context/upsert-single": {"post": {"tags": ["Context"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertContextCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertContextCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertContextCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/upsert-bulk": {"post": {"tags": ["Context"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkContextCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkContextCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertBulkContextCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/get-all": {"get": {"tags": ["Context"], "parameters": [{"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/get-paged": {"get": {"tags": ["Context"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/lookup": {"get": {"tags": ["Context"], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/{contextId}/with-lookups": {"get": {"tags": ["Context"], "parameters": [{"name": "contextId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "includeInactiveLookups", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/bulk/with-lookups": {"post": {"tags": ["Context"], "parameters": [{"name": "includeInactiveLookups", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/lookup/upsert": {"post": {"tags": ["Context"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertLookupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertLookupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertLookupCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/lookup/{lookupId}": {"delete": {"tags": ["Context"], "parameters": [{"name": "lookupId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/lookup/bulk-upsert": {"post": {"tags": ["Context"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkLookupsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkLookupsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertBulkLookupsCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/upsert-single": {"post": {"tags": ["Context"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertTenantContextCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertTenantContextCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertTenantContextCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/upsert-bulk": {"post": {"tags": ["Context"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkTenantContextCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkTenantContextCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertBulkTenantContextCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/get-all": {"get": {"tags": ["Context"], "parameters": [{"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/get-paged": {"get": {"tags": ["Context"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/get-tenant-context": {"get": {"tags": ["Context"], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/lookup": {"get": {"tags": ["Context"], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/{tenantContextId}/with-lookups": {"get": {"tags": ["Context"], "parameters": [{"name": "tenantContextId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "includeInactiveLookups", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/bulk/with-lookups": {"post": {"tags": ["Context"], "parameters": [{"name": "includeInactiveLookups", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/lookup/upsert": {"post": {"tags": ["Context"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertTenantLookupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertTenantLookupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertTenantLookupCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/tenant/lookup/{tenantLookupId}": {"delete": {"tags": ["Context"], "parameters": [{"name": "tenantLookupId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/object-lookup/get-all": {"get": {"tags": ["Context"], "parameters": [{"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "sourceType", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "objectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/context/object-lookup/upsert": {"post": {"tags": ["Context"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectLookupCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectLookupCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertObjectLookupCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/context/object-lookup/{objectLookupId}": {"delete": {"tags": ["Context"], "parameters": [{"name": "objectLookupId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/datatransformation/api/{apiName}/transform": {"post": {"tags": ["DataTransformation"], "parameters": [{"name": "apiName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTransformationResultDtoResult"}}}}}}}, "/api/datatypes": {"get": {"tags": ["DataTypes"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Category", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoPaginatedResult"}}}}}}, "post": {"tags": ["DataTypes"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDataTypeCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDataTypeCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDataTypeCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}}}}}}, "/api/datatypes/{id}": {"get": {"tags": ["DataTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}}}}}, "put": {"tags": ["DataTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDataTypeCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDataTypeCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDataTypeCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}}}}}, "delete": {"tags": ["DataTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/display": {"get": {"tags": ["Display"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "orderBy", "in": "query", "schema": {"type": "string", "default": "SortOrder"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisplayDtoListResult"}}}}}}, "post": {"tags": ["Display"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDisplayCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDisplayCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDisplayCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisplayDtoResult"}}}}}}}, "/api/display/{id}": {"get": {"tags": ["Display"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisplayDtoResult"}}}}}}, "put": {"tags": ["Display"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDisplayCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDisplayCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDisplayCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisplayDtoResult"}}}}}}, "delete": {"tags": ["Display"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/display/actions": {"get": {"tags": ["Display"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "orderBy", "in": "query", "schema": {"type": "string", "default": "Name"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionDtoListResult"}}}}}}, "post": {"tags": ["Display"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateActionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateActionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateActionCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionDtoResult"}}}}}}}, "/api/display/actions/{id}": {"get": {"tags": ["Display"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionDtoResult"}}}}}}}, "/api/display/upsert": {"post": {"tags": ["Display"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisplayWithActionsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DisplayWithActionsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DisplayWithActionsDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisplayWithActionsResponseDtoResult"}}}}}}}, "/api/display/bulk-upsert": {"post": {"tags": ["Display"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDisplayWithActionsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkDisplayWithActionsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkDisplayWithActionsDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDisplayWithActionsResponseDtoResult"}}}}}}}, "/api/fieldmappings": {"get": {"tags": ["FieldMappings"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "ApiName", "in": "query", "schema": {"type": "string"}}, {"name": "SourceType", "in": "query", "schema": {"type": "string"}}, {"name": "ObjectMetadataId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "UserId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "RoleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "TargetObjectName", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoPaginatedResult"}}}}}}, "post": {"tags": ["FieldMappings"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}}}}}}, "/api/fieldmappings/{id}": {"get": {"tags": ["FieldMappings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}}}}}, "put": {"tags": ["FieldMappings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFieldMappingCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateFieldMappingCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateFieldMappingCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}}}}}, "delete": {"tags": ["FieldMappings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/fieldmappings/bulk": {"post": {"tags": ["FieldMappings"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoListResult"}}}}}}}, "/api/integrationapis": {"get": {"tags": ["IntegrationApis"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoPaginatedResult"}}}}}}, "post": {"tags": ["IntegrationApis"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}}}}}}, "/api/integrationapis/{id}": {"get": {"tags": ["IntegrationApis"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}}}}}, "put": {"tags": ["IntegrationApis"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationApiCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationApiCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationApiCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}}}}}, "delete": {"tags": ["IntegrationApis"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/integrationapis/bulk": {"post": {"tags": ["IntegrationApis"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApisCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApisCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApisCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoListResult"}}}}}}}, "/api/integrationapis/with-configuration": {"post": {"tags": ["IntegrationApis"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiWithConfigurationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiWithConfigurationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiWithConfigurationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiWithConfigurationResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiWithConfigurationResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiWithConfigurationResponseDtoResult"}}}}}}}, "/api/integrationconfigurations": {"get": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "IntegrationId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "IntegrationApiId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "ObjectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Direction", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoPaginatedResult"}}}}}}, "post": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}}}}}}, "/api/integrationconfigurations/{id}": {"get": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}}}}}, "put": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationConfigurationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationConfigurationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationConfigurationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}}}}}, "delete": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/integrationconfigurations/bulk": {"post": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoListResult"}}}}}}}, "/api/integrationconfigurations/integrations-with-apis/product/{productId}": {"get": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationWithApiInfoDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationWithApiInfoDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationWithApiInfoDtoListResult"}}}}}}}, "/api/integrations": {"get": {"tags": ["Integrations"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "AuthType", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoPaginatedResult"}}}}}}, "post": {"tags": ["Integrations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}}}}}}, "/api/integrations/{id}": {"get": {"tags": ["Integrations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}}}}}, "put": {"tags": ["Integrations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}}}}}, "delete": {"tags": ["Integrations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/integrations/bulk": {"post": {"tags": ["Integrations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoListResult"}}}}}}}, "/api/integrations/with-api": {"post": {"tags": ["Integrations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationWithApiCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationWithApiCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationWithApiCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateIntegrationWithApiResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationWithApiResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationWithApiResponseDtoResult"}}}}}}}, "/api/metadata": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "DataTypeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "IsVisible", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoPaginatedResult"}}}}}}, "post": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMetadataCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMetadataCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMetadataCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}}}}}}, "/api/metadata/{id}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}}}}}, "put": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMetadataCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMetadataCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMetadataCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/objects": {"get": {"tags": ["Objects"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "FeatureId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoPaginatedResult"}}}}}}, "post": {"tags": ["Objects"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateObjectCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateObjectCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateObjectCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}}}}}}, "/api/objects/with-metadata": {"get": {"tags": ["Objects"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "FeatureId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectWithMetadataDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectWithMetadataDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectWithMetadataDtoPaginatedResult"}}}}}}}, "/api/objects/with-actions": {"get": {"tags": ["Objects"], "parameters": [{"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "ParentObjectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectWithActionsDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectWithActionsDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectWithActionsDtoListResult"}}}}}}}, "/api/objects/{id}": {"get": {"tags": ["Objects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}}}}}}, "/api/objects/{id}/with-metadata": {"get": {"tags": ["Objects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectWithMetadataDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectWithMetadataDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectWithMetadataDtoResult"}}}}}}}, "/api/objects/{id}/metadata": {"get": {"tags": ["Objects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectMetadataDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectMetadataDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectMetadataDtoPaginatedResult"}}}}}}}, "/api/objects/bulk-create-hierarchical": {"post": {"tags": ["Objects"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateHierarchicalObjectsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateHierarchicalObjectsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateHierarchicalObjectsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HierarchicalObjectCreationResultResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HierarchicalObjectCreationResultResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HierarchicalObjectCreationResultResult"}}}}}}}, "/api/objects/validate-hierarchical": {"post": {"tags": ["Objects"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateHierarchicalObjectsQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidateHierarchicalObjectsQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidateHierarchicalObjectsQuery"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HierarchicalObjectValidationResultResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HierarchicalObjectValidationResultResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HierarchicalObjectValidationResultResult"}}}}}}}, "/api/objectvalues/single-value-update": {"patch": {"tags": ["ObjectValues"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SingleValueUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SingleValueUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SingleValueUpdateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/single-value-update-by-refid": {"patch": {"tags": ["ObjectValues"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SingleValueUpdateByRefIdRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SingleValueUpdateByRefIdRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SingleValueUpdateByRefIdRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/upsert-single": {"post": {"tags": ["ObjectValues"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertSingleObjectValueCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertSingleObjectValueCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertSingleObjectValueCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/upsert-bulk": {"post": {"tags": ["ObjectValues"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkObjectValueCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertBulkObjectValueCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertBulkObjectValueCommand"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/upsert-single-with-metadata": {"post": {"tags": ["ObjectValues"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectUpsertWithMetadataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectUpsertWithMetadataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ObjectUpsertWithMetadataRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/upsert-bulk-with-metadata": {"post": {"tags": ["ObjectValues"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectUpsertWithMetadataBulkRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectUpsertWithMetadataBulkRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ObjectUpsertWithMetadataBulkRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/instance-view-by-refid/{refId}": {"get": {"tags": ["ObjectValues"], "parameters": [{"name": "refId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "viewName", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/instances-view/{objectName}": {"get": {"tags": ["ObjectValues"], "parameters": [{"name": "objectName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "createView", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/metadata-key-values/{objectName}/{metadataKey}": {"get": {"tags": ["ObjectValues"], "parameters": [{"name": "objectName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "metadataKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "createView", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/objectvalues/object-lookup-data/{objectLookupId}": {"get": {"tags": ["ObjectValues"], "parameters": [{"name": "objectLookupId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "createView", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/products": {"get": {"tags": ["Products"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPaginatedResult"}}}}}}, "post": {"tags": ["Products"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}}}}}}, "/api/products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}}}}}}, "/api/products/with-subscription": {"post": {"tags": ["Products"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductWithSubscriptionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductWithSubscriptionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductWithSubscriptionCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductWithSubscriptionDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductWithSubscriptionDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductWithSubscriptionDtoResult"}}}}}}}, "/api/roles": {"get": {"tags": ["Roles"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoListResult"}}}}}}, "post": {"tags": ["Roles"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}, "put": {"tags": ["Roles"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/roles/{id}": {"get": {"tags": ["Roles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}}}}}, "delete": {"tags": ["Roles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/roles/{roleId}/actions": {"get": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseResult"}}}}}}, "put": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseResult"}}}}}}}, "/api/roles/bulk/actions": {"put": {"tags": ["Roles"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateRoleActionsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateRoleActionsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateRoleActionsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleActionsResponseListResult"}}}}}}}, "/api/subscriptions": {"get": {"tags": ["Subscriptions"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}}}}}, "post": {"tags": ["Subscriptions"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}}}}}}, "/api/subscriptions/{id}": {"get": {"tags": ["Subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}}}}}, "put": {"tags": ["Subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoResult"}}}}}}, "delete": {"tags": ["Subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/subscriptions/by-product/{productId}": {"get": {"tags": ["Subscriptions"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}}}}}}, "/api/subscriptions/by-status/{status}": {"get": {"tags": ["Subscriptions"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}}}}}}, "/api/subscriptions/active": {"get": {"tags": ["Subscriptions"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}}}}}}, "/api/subscriptions/expired": {"get": {"tags": ["Subscriptions"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPaginatedResult"}}}}}}}, "/api/templates": {"get": {"tags": ["Templates"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Stage", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeDeleted", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}}}}}, "post": {"tags": ["Templates"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTemplateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTemplateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTemplateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}}}}}}, "/api/templates/{id}": {"get": {"tags": ["Templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}}}}}, "put": {"tags": ["Templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTemplateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTemplateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTemplateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}}}}}, "delete": {"tags": ["Templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/templates/{id}/publish": {"post": {"tags": ["Templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoResult"}}}}}}}, "/api/templates/by-product/{productId}": {"get": {"tags": ["Templates"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "stage", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}}}}}}, "/api/templates/by-stage/{stage}": {"get": {"tags": ["Templates"], "parameters": [{"name": "stage", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TemplateDtoPaginatedResult"}}}}}}}, "/api/tenants": {"get": {"tags": ["Tenants"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoListResult"}}}}}}, "post": {"tags": ["Tenants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}, "put": {"tags": ["Tenants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants/{id}": {"get": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantWithProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantWithProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantWithProductDtoResult"}}}}}}, "delete": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants/{id}/activate": {"post": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants/{id}/deactivate": {"post": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants/upsert": {"post": {"tags": ["Tenants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoResult"}}}}}}}, "/api/tokens": {"post": {"tags": ["Tokens"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}}}}}}, "/api/tokens/refresh": {"post": {"tags": ["Tokens"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}}}}}}, "/api/users": {"get": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoListResult"}}}}}}, "post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/paginated": {"get": {"tags": ["Users"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchString", "in": "query", "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoObjectPagedResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoObjectPagedResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoObjectPagedResponse"}}}}}}}, "/api/users/{id}": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoResult"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/{id}/change-password": {"put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/forgot-password": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/reset-password": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/create": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}}, "/api/users/bulk-create": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersResponseApiResponse"}}}}}}}, "/api/users/bulk-update-roles": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponseApiResponse"}}}}}}}, "/api/users/{userId}/roles": {"get": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserRoleDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserRoleDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRoleDtoListApiResponse"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/users/{userId}/roles/assign": {"post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/users/{userId}/roles/remove": {"post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/users/roles/{roleName}/users": {"get": {"tags": ["Users"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoListApiResponse"}}}}}}}}, "components": {"schemas": {"ActionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "endpointTemplate": {"type": "string", "nullable": true}, "navigationTarget": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "buttonStyle": {"type": "string", "nullable": true}, "confirmationMessage": {"type": "string", "nullable": true}, "successMessage": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "modifiedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ActionDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ActionDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ActionDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ActionDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ActionResultDto": {"type": "object", "properties": {"actionId": {"type": "string", "format": "uuid"}, "displayActionId": {"type": "string", "format": "uuid"}, "action": {"$ref": "#/components/schemas/ActionDto"}, "displayAction": {"$ref": "#/components/schemas/DisplayActionDto"}, "actionWasCreated": {"type": "boolean"}, "displayActionWasCreated": {"type": "boolean"}}, "additionalProperties": false}, "ActionStructureDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "endpointTemplate": {"maxLength": 500, "type": "string", "nullable": true}, "navigationTarget": {"maxLength": 500, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "type": "string", "nullable": true}, "buttonStyle": {"maxLength": 50, "type": "string", "nullable": true}, "confirmationMessage": {"maxLength": 1000, "type": "string", "nullable": true}, "successMessage": {"maxLength": 1000, "type": "string", "nullable": true}, "errorMessage": {"maxLength": 1000, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "accessLevel": {"maxLength": 50, "type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "isVisibleInToolbar": {"type": "boolean"}, "isVisibleInContextMenu": {"type": "boolean"}}, "additionalProperties": false}, "ActionWithDisplayActionDto": {"required": ["name", "objectId"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "endpointTemplate": {"maxLength": 500, "type": "string", "nullable": true}, "navigationTarget": {"maxLength": 500, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "type": "string", "nullable": true}, "buttonStyle": {"maxLength": 50, "type": "string", "nullable": true}, "confirmationMessage": {"type": "string", "nullable": true}, "successMessage": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "objectId": {"type": "string", "format": "uuid"}, "accessLevel": {"maxLength": 50, "type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "isVisibleInToolbar": {"type": "boolean"}, "isVisibleInContextMenu": {"type": "boolean"}, "isVisibleInRowActions": {"type": "boolean"}, "displayActionIsActive": {"type": "boolean"}}, "additionalProperties": false}, "ApiConfigurationInfoDto": {"type": "object", "properties": {"apiId": {"type": "string", "format": "uuid"}, "apiName": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isApiActive": {"type": "boolean"}, "configurationId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "direction": {"type": "string", "nullable": true}, "isConfigActive": {"type": "boolean"}, "configCreatedAt": {"type": "string", "format": "date-time"}, "configCreatedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "BooleanResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "BulkCreateUsersCommand": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserCommand"}, "nullable": true}, "continueOnError": {"type": "boolean"}, "validateBeforeCreate": {"type": "boolean"}, "productId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BulkCreateUsersResponse": {"type": "object", "properties": {"totalRequested": {"type": "integer", "format": "int32"}, "successfullyCreated": {"type": "integer", "format": "int32"}, "failed": {"type": "integer", "format": "int32"}, "createdUsers": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/BulkUserCreationError"}, "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkCreateUsersResponseApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/BulkCreateUsersResponse"}}, "additionalProperties": false}, "BulkCreationSummaryDto": {"type": "object", "properties": {"totalRecordsCreated": {"type": "integer", "format": "int32"}, "createdEntityIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "operationTimestamp": {"type": "string", "format": "date-time"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkDisplayWithActionsDto": {"required": ["displays"], "type": "object", "properties": {"displays": {"minItems": 1, "type": "array", "items": {"$ref": "#/components/schemas/DisplayWithActionsDto"}}}, "additionalProperties": false}, "BulkDisplayWithActionsResponseDto": {"type": "object", "properties": {"displayResults": {"type": "array", "items": {"$ref": "#/components/schemas/DisplayWithActionsResponseDto"}, "nullable": true}, "totalDisplaysProcessed": {"type": "integer", "format": "int32"}, "totalDisplaysCreated": {"type": "integer", "format": "int32"}, "totalDisplaysUpdated": {"type": "integer", "format": "int32"}, "totalActionsProcessed": {"type": "integer", "format": "int32"}, "totalActionsCreated": {"type": "integer", "format": "int32"}, "totalActionsUpdated": {"type": "integer", "format": "int32"}, "totalDisplayActionsProcessed": {"type": "integer", "format": "int32"}, "totalDisplayActionsCreated": {"type": "integer", "format": "int32"}, "totalDisplayActionsUpdated": {"type": "integer", "format": "int32"}, "operationSummary": {"type": "string", "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isSuccessful": {"type": "boolean"}}, "additionalProperties": false}, "BulkDisplayWithActionsResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/BulkDisplayWithActionsResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "BulkUpdateRoleActionsRequest": {"type": "object", "properties": {"roleActions": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateRoleActionsRequest"}, "nullable": true}}, "additionalProperties": false}, "BulkUpdateUserRolesCommand": {"type": "object", "properties": {"userRoleUpdates": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleUpdateRequest"}, "nullable": true}, "continueOnError": {"type": "boolean"}, "validateBeforeUpdate": {"type": "boolean"}, "productId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BulkUpdateUserRolesResponse": {"type": "object", "properties": {"totalRequested": {"type": "integer", "format": "int32"}, "successfullyUpdated": {"type": "integer", "format": "int32"}, "failed": {"type": "integer", "format": "int32"}, "updatedUsers": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleUpdateResult"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/BulkUserRoleUpdateError"}, "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkUpdateUserRolesResponseApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponse"}}, "additionalProperties": false}, "BulkUserCreationError": {"type": "object", "properties": {"userIndex": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "errorDetails": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkUserRoleUpdateError": {"type": "object", "properties": {"userIndex": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "attemptedRoleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "errorDetails": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordRequest": {"type": "object", "properties": {"currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "confirmNewPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ComprehensiveSubscriptionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tenantId": {"type": "string", "nullable": true}, "tenantName": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "subscriptionType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "templateDetails": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "metadataCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isExpired": {"type": "boolean", "readOnly": true}, "daysUntilExpiration": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ComprehensiveSubscriptionResponseDto": {"type": "object", "properties": {"subscriptions": {"type": "array", "items": {"$ref": "#/components/schemas/ComprehensiveSubscriptionDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "summary": {"$ref": "#/components/schemas/ComprehensiveSubscriptionSummaryDto"}}, "additionalProperties": false}, "ComprehensiveSubscriptionResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ComprehensiveSubscriptionResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ComprehensiveSubscriptionSummaryDto": {"type": "object", "properties": {"activeSubscriptions": {"type": "integer", "format": "int32"}, "expiredSubscriptions": {"type": "integer", "format": "int32"}, "expiringIn30Days": {"type": "integer", "format": "int32"}, "uniqueTenants": {"type": "integer", "format": "int32"}, "uniqueProducts": {"type": "integer", "format": "int32"}, "subscriptionsByStatus": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "subscriptionsByType": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "ContextUpsertItem": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateActionCommand": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "endpointTemplate": {"maxLength": 500, "type": "string", "nullable": true}, "navigationTarget": {"maxLength": 500, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "type": "string", "nullable": true}, "buttonStyle": {"maxLength": 50, "type": "string", "nullable": true}, "confirmationMessage": {"type": "string", "nullable": true}, "successMessage": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateDataTypeCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean"}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean"}, "allowsCustomOptions": {"type": "boolean"}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "fileSizeErrorMessage": {"type": "string", "nullable": true}, "selectType": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateDisplayCommand": {"required": ["displayName", "name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "displayName": {"maxLength": 255, "minLength": 1, "type": "string"}, "isDefault": {"type": "boolean"}, "routeTemplate": {"maxLength": 500, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateFieldMappingCommand": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateFieldMappingRequest": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateFieldMappingsCommand": {"type": "object", "properties": {"fieldMappings": {"type": "array", "items": {"$ref": "#/components/schemas/CreateFieldMappingRequest"}, "nullable": true}}, "additionalProperties": false}, "CreateHierarchicalObjectsCommand": {"required": ["objects", "productId"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/HierarchicalObjectDto"}}}, "additionalProperties": false}, "CreateIntegrationApiCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationApiDto": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationApiRequestDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationApiWithConfigurationCommand": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "direction": {"type": "string", "nullable": true}, "isConfigActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationApiWithConfigurationResponseDto": {"type": "object", "properties": {"integrationApi": {"$ref": "#/components/schemas/IntegrationApiDetailsDto"}, "configuration": {"$ref": "#/components/schemas/IntegrationConfigurationDetailsDto"}, "summary": {"$ref": "#/components/schemas/OperationSummaryDto"}}, "additionalProperties": false}, "CreateIntegrationApiWithConfigurationResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/CreateIntegrationApiWithConfigurationResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "CreateIntegrationApisCommand": {"type": "object", "properties": {"integrationApis": {"type": "array", "items": {"$ref": "#/components/schemas/CreateIntegrationApiDto"}, "nullable": true}}, "additionalProperties": false}, "CreateIntegrationCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}}, "additionalProperties": false}, "CreateIntegrationConfigurationCommand": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationConfigurationRequest": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationConfigurationRequestDto": {"type": "object", "properties": {"objectId": {"type": "string", "format": "uuid"}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationConfigurationsCommand": {"type": "object", "properties": {"configurations": {"type": "array", "items": {"$ref": "#/components/schemas/CreateIntegrationConfigurationRequest"}, "nullable": true}}, "additionalProperties": false}, "CreateIntegrationRequest": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}}, "additionalProperties": false}, "CreateIntegrationWithApiCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequencyMinutes": {"type": "integer", "format": "int32", "nullable": true}, "integrationApi": {"$ref": "#/components/schemas/CreateIntegrationApiRequestDto"}, "configuration": {"$ref": "#/components/schemas/CreateIntegrationConfigurationRequestDto"}}, "additionalProperties": false}, "CreateIntegrationWithApiResponseDto": {"type": "object", "properties": {"integration": {"$ref": "#/components/schemas/CreatedIntegrationDetailsDto"}, "integrationApi": {"$ref": "#/components/schemas/CreatedIntegrationApiDetailsDto"}, "configuration": {"$ref": "#/components/schemas/CreatedIntegrationConfigurationDetailsDto"}, "summary": {"$ref": "#/components/schemas/BulkCreationSummaryDto"}}, "additionalProperties": false}, "CreateIntegrationWithApiResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/CreateIntegrationWithApiResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "CreateIntegrationsCommand": {"type": "object", "properties": {"integrations": {"type": "array", "items": {"$ref": "#/components/schemas/CreateIntegrationRequest"}, "nullable": true}}, "additionalProperties": false}, "CreateMetadataCommand": {"type": "object", "properties": {"metadataKey": {"type": "string", "nullable": true}, "dataTypeId": {"type": "string", "format": "uuid"}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}}, "additionalProperties": false}, "CreateObjectCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateProductCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductStructureCommand": {"required": ["products"], "type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/ProductStructureDto"}}}, "additionalProperties": false}, "CreateProductWithSubscriptionCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "subscriptionType": {"type": "string", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"type": "string", "nullable": true}, "subscriptionVersion": {"type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "templateDetails": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true}}, "additionalProperties": false}, "CreateRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "isSystemRole": {"type": "boolean"}, "permissions": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateSubscriptionDto": {"required": ["productId", "status", "subscriptionType"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "subscriptionType": {"maxLength": 100, "minLength": 1, "type": "string"}, "status": {"maxLength": 50, "minLength": 1, "type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"maxLength": 100, "type": "string", "nullable": true}, "version": {"maxLength": 50, "type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "templateDetails": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateTemplateDto": {"required": ["name", "stage", "template<PERSON>son", "version"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 1, "type": "string"}, "version": {"maxLength": 50, "minLength": 1, "type": "string"}, "stage": {"maxLength": 20, "minLength": 1, "type": "string"}, "templateJson": {"minLength": 1, "type": "string"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateUserCommand": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isMFAEnabled": {"type": "boolean"}, "integrationSourceId": {"type": "string", "format": "uuid", "nullable": true}, "externalUserId": {"type": "string", "nullable": true}, "externalUserData": {"type": "string", "nullable": true}, "mustChangePassword": {"type": "boolean"}, "objectId": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "roleIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "CreatedIntegrationApiDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreatedIntegrationConfigurationDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationId": {"type": "string", "format": "uuid"}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreatedIntegrationDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreatedObjectInfo": {"type": "object", "properties": {"objectId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "level": {"type": "integer", "format": "int32"}, "metadataFieldsCount": {"type": "integer", "format": "int32"}, "instanceValuesCount": {"type": "integer", "format": "int32"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/CreatedObjectInfo"}, "nullable": true}}, "additionalProperties": false}, "CreatedProductInfo": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "wasCreated": {"type": "boolean"}, "metadataFieldsCreated": {"type": "integer", "format": "int32"}, "metadataFieldsExisting": {"type": "integer", "format": "int32"}, "objectsCreated": {"type": "integer", "format": "int32"}, "objectsExisting": {"type": "integer", "format": "int32"}, "rootObjects": {"type": "array", "items": {"$ref": "#/components/schemas/ProductStructureCreatedObjectInfo"}, "nullable": true}}, "additionalProperties": false}, "DataTransformationResultDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "refId": {"type": "string", "format": "uuid", "nullable": true}, "objectValuesCreated": {"type": "integer", "format": "int32"}, "processingTimeMs": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DataTransformationResultDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/DataTransformationResultDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "DataTypeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean"}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean"}, "allowsCustomOptions": {"type": "boolean"}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "fileSizeErrorMessage": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean", "nullable": true}, "isReadonly": {"type": "boolean", "nullable": true}, "selectType": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "DataTypeDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DataTypeDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "DataTypeDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/DataTypeDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "DataTypeInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean", "nullable": true}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean", "nullable": true}, "allowsCustomOptions": {"type": "boolean", "nullable": true}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "fileSizeErrorMessage": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean", "nullable": true}, "isReadonly": {"type": "boolean", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "DisplayActionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "displayId": {"type": "string", "format": "uuid"}, "actionId": {"type": "string", "format": "uuid"}, "accessLevel": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "isVisibleInToolbar": {"type": "boolean"}, "isVisibleInContextMenu": {"type": "boolean"}, "isVisibleInRowActions": {"type": "boolean"}, "isActive": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "modifiedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "display": {"$ref": "#/components/schemas/DisplayDto"}, "action": {"$ref": "#/components/schemas/ActionDto"}}, "additionalProperties": false}, "DisplayDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "routeTemplate": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "modifiedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "DisplayDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DisplayDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "DisplayDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/DisplayDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "DisplayStructureDto": {"required": ["displayName", "name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "displayName": {"maxLength": 255, "minLength": 1, "type": "string"}, "isDefault": {"type": "boolean"}, "routeTemplate": {"maxLength": 500, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/ActionStructureDto"}, "nullable": true}}, "additionalProperties": false}, "DisplayWithActionsDto": {"required": ["actions", "displayName", "name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "displayName": {"maxLength": 255, "minLength": 1, "type": "string"}, "isDefault": {"type": "boolean"}, "routeTemplate": {"maxLength": 500, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/ActionWithDisplayActionDto"}}}, "additionalProperties": false}, "DisplayWithActionsResponseDto": {"type": "object", "properties": {"displayId": {"type": "string", "format": "uuid"}, "display": {"$ref": "#/components/schemas/DisplayDto"}, "actionResults": {"type": "array", "items": {"$ref": "#/components/schemas/ActionResultDto"}, "nullable": true}, "displayWasCreated": {"type": "boolean"}, "operationSummary": {"type": "string", "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DisplayWithActionsResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/DisplayWithActionsResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ForgotPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HierarchicalEntityDataResponseDto": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/ProductHierarchicalDto"}, "nullable": true}, "totalObjectsCount": {"type": "integer", "format": "int32", "readOnly": true}, "totalMetadataCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "HierarchicalEntityDataResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/HierarchicalEntityDataResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "HierarchicalObjectCreationResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "totalObjectsCreated": {"type": "integer", "format": "int32"}, "totalMetadataCreated": {"type": "integer", "format": "int32"}, "totalObjectMetadataCreated": {"type": "integer", "format": "int32"}, "totalObjectValuesCreated": {"type": "integer", "format": "int32"}, "createdObjects": {"type": "array", "items": {"$ref": "#/components/schemas/CreatedObjectInfo"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "HierarchicalObjectCreationResultResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/HierarchicalObjectCreationResult"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "HierarchicalObjectDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "metaJson": {"type": "object", "additionalProperties": {"nullable": true}, "nullable": true}, "metaValues": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "childObjects": {"type": "array", "items": {"$ref": "#/components/schemas/HierarchicalObjectDto"}, "nullable": true}}, "additionalProperties": false}, "HierarchicalObjectValidationResult": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "totalObjects": {"type": "integer", "format": "int32"}, "maxDepth": {"type": "integer", "format": "int32"}, "estimatedMetadataFields": {"type": "integer", "format": "int32"}, "estimatedValues": {"type": "integer", "format": "int32"}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "HierarchicalObjectValidationResultResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/HierarchicalObjectValidationResult"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "HttpStatusCode": {"enum": [100, 101, 102, 103, 200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 421, 422, 423, 424, 426, 428, 429, 431, 451, 500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511], "type": "integer", "format": "int32"}, "IntegrationApiDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "IntegrationConfigurationDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationId": {"type": "string", "format": "uuid"}, "integrationName": {"type": "string", "nullable": true}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "IntegrationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}, "lastSyncAt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "IntegrationDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "IntegrationDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "IntegrationDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/IntegrationDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "IntegrationWithApiInfoDto": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "integrationName": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "isIntegrationActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}, "lastSyncAt": {"type": "string", "format": "date-time", "nullable": true}, "integrationCreatedAt": {"type": "string", "format": "date-time"}, "integrationCreatedBy": {"type": "string", "format": "uuid", "nullable": true}, "configuredApis": {"type": "array", "items": {"$ref": "#/components/schemas/ApiConfigurationInfoDto"}, "nullable": true}, "totalApis": {"type": "integer", "format": "int32", "readOnly": true}, "activeApis": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "IntegrationWithApiInfoDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationWithApiInfoDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "LookupRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "value": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "value1": {"type": "string", "nullable": true}, "value2": {"type": "string", "nullable": true}, "showSequence": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "MetadataDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "dataTypeId": {"type": "string", "format": "uuid"}, "dataTypeName": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "MetadataDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "MetadataDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/MetadataDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "MetadataInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean", "nullable": true}, "isReadonly": {"type": "boolean", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "maxFileSizeBytesErrorMessage": {"type": "string", "nullable": true}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean", "nullable": true}, "allowsCustomOptions": {"type": "boolean", "nullable": true}, "contextId": {"type": "string", "format": "uuid", "nullable": true}, "tenantContextId": {"type": "string", "format": "uuid", "nullable": true}, "objectLookupId": {"type": "string", "format": "uuid", "nullable": true}, "dataTypeName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "dataType": {"$ref": "#/components/schemas/DataTypeInfoDto"}, "metadataLink": {"$ref": "#/components/schemas/MetadataLinkInfoDto"}}, "additionalProperties": false}, "MetadataLinkInfoDto": {"type": "object", "properties": {"objectMetaDataId": {"type": "string", "format": "uuid", "nullable": true}, "isUnique": {"type": "boolean"}, "isActive": {"type": "boolean"}, "shouldVisibleInList": {"type": "boolean", "nullable": true}, "shouldVisibleInEdit": {"type": "boolean", "nullable": true}, "shouldVisibleInCreate": {"type": "boolean", "nullable": true}, "shouldVisibleInView": {"type": "boolean", "nullable": true}, "isCalculate": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "MetadataStructureDto": {"required": ["name", "type"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"maxLength": 255, "minLength": 1, "type": "string"}, "type": {"minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "required": {"type": "boolean"}, "isActive": {"type": "boolean"}, "defaultValue": {"type": "string", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}}, "additionalProperties": false}, "MetadataWithValuesDto": {"type": "object", "properties": {"metadata": {"$ref": "#/components/schemas/MetadataInfoDto"}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/ValueInfoDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectActionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "endpointTemplate": {"type": "string", "nullable": true}, "navigationTarget": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "buttonStyle": {"type": "string", "nullable": true}, "confirmationMessage": {"type": "string", "nullable": true}, "successMessage": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "displayActionId": {"type": "string", "format": "uuid"}, "displayId": {"type": "string", "format": "uuid"}, "accessLevel": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "isVisibleInToolbar": {"type": "boolean"}, "isVisibleInContextMenu": {"type": "boolean"}, "isVisibleInRowActions": {"type": "boolean"}}, "additionalProperties": false}, "ObjectDisplayActionDto": {"type": "object", "properties": {"actionId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "endpointTemplate": {"type": "string", "nullable": true}, "navigationTarget": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "buttonStyle": {"type": "string", "nullable": true}, "confirmationMessage": {"type": "string", "nullable": true}, "successMessage": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "actionIsActive": {"type": "boolean"}, "actionCreatedAt": {"type": "string", "format": "date-time"}, "actionModifiedAt": {"type": "string", "format": "date-time"}, "actionCreatedBy": {"type": "string", "format": "uuid", "nullable": true}, "actionModifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "actionIsDeleted": {"type": "boolean"}, "displayActionId": {"type": "string", "format": "uuid"}, "accessLevel": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "isVisibleInToolbar": {"type": "boolean"}, "isVisibleInContextMenu": {"type": "boolean"}, "isVisibleInRowActions": {"type": "boolean"}, "displayActionIsActive": {"type": "boolean"}, "displayActionCreatedAt": {"type": "string", "format": "date-time"}, "displayActionModifiedAt": {"type": "string", "format": "date-time"}, "displayActionCreatedBy": {"type": "string", "format": "uuid", "nullable": true}, "displayActionModifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "displayActionIsDeleted": {"type": "boolean"}}, "additionalProperties": false}, "ObjectDisplayDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "routeTemplate": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "modifiedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectDisplayActionDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "childObjectsCount": {"type": "integer", "format": "int32"}, "metadataCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ObjectDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ObjectDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ObjectDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ObjectHierarchicalDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "isActive": {"type": "boolean"}, "icon": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "hierarchyLevel": {"type": "integer", "format": "int32"}, "hierarchyPath": {"type": "string", "nullable": true}, "metadata": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataWithValuesDto"}, "nullable": true}, "childObjects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectHierarchicalDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectMetadataDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "metadataId": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "metadataDisplayLabel": {"type": "string", "nullable": true}, "isUnique": {"type": "boolean"}, "isActive": {"type": "boolean"}, "valuesCount": {"type": "integer", "format": "int32"}, "shouldVisibleInList": {"type": "boolean"}, "shouldVisibleInEdit": {"type": "boolean"}, "shouldVisibleInCreate": {"type": "boolean"}, "shouldVisibleInView": {"type": "boolean"}, "isCalculate": {"type": "boolean"}, "dataTypeId": {"type": "string", "format": "uuid"}, "dataTypeName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ObjectMetadataDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectMetadataDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ObjectStructureDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"maxLength": 255, "minLength": 1, "type": "string"}, "type": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "metadata": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataStructureDto"}, "nullable": true}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectStructureDto"}, "nullable": true}, "displays": {"type": "array", "items": {"$ref": "#/components/schemas/DisplayStructureDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectUpsertWithMetadataBulkRequest": {"type": "object", "properties": {"objects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectUpsertWithMetadataRequest"}, "nullable": true}, "batchSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ObjectUpsertWithMetadataRequest": {"type": "object", "properties": {"metadataProperties": {"type": "object", "additionalProperties": {"nullable": true}, "nullable": true}}, "additionalProperties": false}, "ObjectValueData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid"}, "parentObjectValueId": {"type": "string", "format": "uuid", "nullable": true}, "value": {"type": "string", "nullable": true}, "isRefId": {"type": "boolean", "nullable": true}, "isUserId": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "ObjectWithActionsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectActionDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectWithActionsDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectWithActionsDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ObjectWithMetadataDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "childObjectsCount": {"type": "integer", "format": "int32"}, "metadata": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectMetadataDto"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ObjectWithMetadataDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectWithMetadataDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ObjectWithMetadataDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ObjectWithMetadataDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "OperationSummaryDto": {"type": "object", "properties": {"totalRecordsCreated": {"type": "integer", "format": "int32"}, "createdEntityIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "operationTimestamp": {"type": "string", "format": "date-time"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProcessingMetrics": {"type": "object", "properties": {"totalProcessingTimeMs": {"type": "integer", "format": "int64"}, "validationTimeMs": {"type": "integer", "format": "int64"}, "databaseTimeMs": {"type": "integer", "format": "int64"}, "databaseQueriesCount": {"type": "integer", "format": "int32"}, "maxHierarchyDepth": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ProductDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ProductDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ProductDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ProductHierarchicalDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "metadata": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataWithValuesDto"}, "nullable": true}, "rootObjects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectHierarchicalDto"}, "nullable": true}}, "additionalProperties": false}, "ProductStructureCreatedObjectInfo": {"type": "object", "properties": {"objectId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "wasCreated": {"type": "boolean"}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "level": {"type": "integer", "format": "int32"}, "metadataFieldsCreated": {"type": "integer", "format": "int32"}, "metadataFieldsExisting": {"type": "integer", "format": "int32"}, "displaysCreated": {"type": "integer", "format": "int32"}, "displaysExisting": {"type": "integer", "format": "int32"}, "actionsCreated": {"type": "integer", "format": "int32"}, "actionsExisting": {"type": "integer", "format": "int32"}, "displayActionsCreated": {"type": "integer", "format": "int32"}, "displayActionsExisting": {"type": "integer", "format": "int32"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/ProductStructureCreatedObjectInfo"}, "nullable": true}}, "additionalProperties": false}, "ProductStructureCreationResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "totalProductsCreated": {"type": "integer", "format": "int32"}, "totalProductsExisting": {"type": "integer", "format": "int32"}, "totalObjectsCreated": {"type": "integer", "format": "int32"}, "totalObjectsExisting": {"type": "integer", "format": "int32"}, "totalMetadataCreated": {"type": "integer", "format": "int32"}, "totalMetadataExisting": {"type": "integer", "format": "int32"}, "totalObjectMetadataCreated": {"type": "integer", "format": "int32"}, "totalObjectMetadataExisting": {"type": "integer", "format": "int32"}, "totalDisplaysCreated": {"type": "integer", "format": "int32"}, "totalDisplaysExisting": {"type": "integer", "format": "int32"}, "totalActionsCreated": {"type": "integer", "format": "int32"}, "totalActionsExisting": {"type": "integer", "format": "int32"}, "totalDisplayActionsCreated": {"type": "integer", "format": "int32"}, "totalDisplayActionsExisting": {"type": "integer", "format": "int32"}, "totalRolesCreated": {"type": "integer", "format": "int32"}, "createdProducts": {"type": "array", "items": {"$ref": "#/components/schemas/CreatedProductInfo"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}, "metrics": {"$ref": "#/components/schemas/ProcessingMetrics"}}, "additionalProperties": false}, "ProductStructureCreationResultResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ProductStructureCreationResult"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ProductStructureDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"maxLength": 255, "minLength": 1, "type": "string"}, "type": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "version": {"maxLength": 50, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "metadata": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataStructureDto"}, "nullable": true}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectStructureDto"}, "nullable": true}}, "additionalProperties": false}, "ProductSubscriptionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "subscriptionType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "productName": {"type": "string", "nullable": true}, "metadataCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ProductWithSubscriptionDto": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/ProductDto"}, "subscription": {"$ref": "#/components/schemas/ProductSubscriptionDto"}}, "additionalProperties": false}, "ProductWithSubscriptionDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ProductWithSubscriptionDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "RefreshTokenRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterUserRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoleActionsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}, "actionId": {"type": "string", "format": "uuid"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "RoleActionsResponse": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/RoleActionsDto"}, "nullable": true}}, "additionalProperties": false}, "RoleActionsResponseListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleActionsResponse"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "RoleActionsResponseResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/RoleActionsResponse"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "RoleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "normalizedName": {"type": "string", "nullable": true}, "concurrencyStamp": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "isSystemRole": {"type": "boolean"}, "permissions": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}}, "additionalProperties": false}, "RoleDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "RoleDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/RoleDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "SingleValueUpdateByRefIdRequest": {"type": "object", "properties": {"refId": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SingleValueUpdateRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "SubscriptionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "subscriptionType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "templateDetails": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true}, "isActive": {"type": "boolean"}, "metadataCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "SubscriptionDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "SubscriptionDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/SubscriptionDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "TemplateDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "stage": {"type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "isDeleted": {"type": "boolean"}}, "additionalProperties": false}, "TemplateDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TemplateDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TemplateDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "TenantContextUpsertItem": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "TenantDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "connectionString": {"type": "string", "nullable": true}, "adminEmail": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "validUpto": {"type": "string", "format": "date-time"}, "issuer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TenantDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "TenantDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TenantDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "TenantWithProductDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "connectionString": {"type": "string", "nullable": true}, "adminEmail": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "validUpto": {"type": "string", "format": "date-time"}, "issuer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantWithProductDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TenantWithProductDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "TokenRequest": {"type": "object", "properties": {"usernameOrEmail": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TokenResponse": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiresIn": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "TokenResponseResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TokenResponse"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UnifiedHierarchicalEntityDataResponseDto": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/UnifiedProductHierarchicalDto"}, "nullable": true}, "totalObjectsCount": {"type": "integer", "format": "int32", "readOnly": true}, "totalMetadataCount": {"type": "integer", "format": "int32", "readOnly": true}, "maxHierarchyDepth": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "UnifiedHierarchicalEntityDataResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UnifiedHierarchicalEntityDataResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UnifiedMetadataResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean", "nullable": true}, "isReadonly": {"type": "boolean", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean", "nullable": true}, "allowsCustomOptions": {"type": "boolean", "nullable": true}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "dataTypeName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "metadataLinkId": {"type": "string", "format": "uuid", "nullable": true}, "isUnique": {"type": "boolean", "nullable": true}, "isVisibleInList": {"type": "boolean", "nullable": true}, "isVisibleInEdit": {"type": "boolean", "nullable": true}, "isVisibleInCreate": {"type": "boolean", "nullable": true}, "isVisibleInView": {"type": "boolean", "nullable": true}, "isCalculated": {"type": "boolean", "nullable": true}, "contextId": {"type": "string", "format": "uuid", "nullable": true}, "tenantContextId": {"type": "string", "format": "uuid", "nullable": true}, "objectLookupId": {"type": "string", "format": "uuid", "nullable": true}, "isEditable": {"type": "boolean", "nullable": true}, "isSearchable": {"type": "boolean", "nullable": true}, "isSortable": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "displayFormat": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UnifiedMetadataWithValuesResponseDto": {"type": "object", "properties": {"metadata": {"$ref": "#/components/schemas/UnifiedMetadataResponseDto"}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/ValueInfoDto"}, "nullable": true}}, "additionalProperties": false}, "UnifiedObjectHierarchicalDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "isActive": {"type": "boolean"}, "icon": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "hierarchyLevel": {"type": "integer", "format": "int32"}, "hierarchyPath": {"type": "string", "nullable": true}, "metadata": {"type": "array", "items": {"$ref": "#/components/schemas/UnifiedMetadataWithValuesResponseDto"}, "nullable": true}, "childObjects": {"type": "array", "items": {"$ref": "#/components/schemas/UnifiedObjectHierarchicalDto"}, "nullable": true}, "displays": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectDisplayDto"}, "nullable": true}}, "additionalProperties": false}, "UnifiedProductHierarchicalDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "metadata": {"type": "array", "items": {"$ref": "#/components/schemas/UnifiedMetadataWithValuesResponseDto"}, "nullable": true}, "rootObjects": {"type": "array", "items": {"$ref": "#/components/schemas/UnifiedObjectHierarchicalDto"}, "nullable": true}}, "additionalProperties": false}, "UpdateDataTypeCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean"}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean"}, "allowsCustomOptions": {"type": "boolean"}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "fileSizeErrorMessage": {"type": "string", "nullable": true}, "selectType": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateDisplayCommand": {"required": ["displayName", "name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "displayName": {"maxLength": 255, "minLength": 1, "type": "string"}, "isDefault": {"type": "boolean"}, "routeTemplate": {"maxLength": 500, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateFieldMappingCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationId": {"type": "string", "format": "uuid"}, "apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateIntegrationApiCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateIntegrationCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}}, "additionalProperties": false}, "UpdateIntegrationConfigurationCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateMetadataCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "dataTypeId": {"type": "string", "format": "uuid"}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}}, "additionalProperties": false}, "UpdateProductCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateRoleActionsRequest": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid"}, "actionIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "UpdateRoleRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "isSystemRole": {"type": "boolean"}, "permissions": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateSubscriptionDto": {"required": ["productId", "status", "subscriptionType"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "subscriptionType": {"maxLength": 100, "minLength": 1, "type": "string"}, "status": {"maxLength": 50, "minLength": 1, "type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"maxLength": 100, "type": "string", "nullable": true}, "version": {"maxLength": 50, "type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "templateDetails": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateTemplateDto": {"type": "object", "properties": {"name": {"maxLength": 255, "type": "string", "nullable": true}, "version": {"maxLength": 50, "type": "string", "nullable": true}, "stage": {"maxLength": 20, "type": "string", "nullable": true}, "templateJson": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "UpdateUserRequest": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpsertBulkContextCommand": {"type": "object", "properties": {"contexts": {"type": "array", "items": {"$ref": "#/components/schemas/ContextUpsertItem"}, "nullable": true}, "batchSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpsertBulkLookupsCommand": {"type": "object", "properties": {"contextId": {"type": "string", "format": "uuid"}, "lookups": {"type": "array", "items": {"$ref": "#/components/schemas/LookupRequest"}, "nullable": true}}, "additionalProperties": false}, "UpsertBulkObjectValueCommand": {"type": "object", "properties": {"instances": {"type": "array", "items": {"$ref": "#/components/schemas/UpsertSingleObjectValueCommand"}, "nullable": true}}, "additionalProperties": false}, "UpsertBulkTenantContextCommand": {"type": "object", "properties": {"tenantContexts": {"type": "array", "items": {"$ref": "#/components/schemas/TenantContextUpsertItem"}, "nullable": true}, "batchSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpsertContextCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpsertLookupCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "contextId": {"type": "string", "format": "uuid"}, "value": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "value1": {"type": "string", "nullable": true}, "value2": {"type": "string", "nullable": true}, "showSequence": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpsertObjectLookupCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectId": {"type": "string", "format": "uuid", "nullable": true}, "displayField": {"type": "string", "nullable": true}, "valueField": {"type": "string", "nullable": true}, "metadataFieldForDisplay": {"type": "string", "nullable": true}, "metadataFieldForValue": {"type": "string", "nullable": true}, "supportsTenantFiltering": {"type": "boolean"}, "sortBy": {"type": "string", "nullable": true}, "sortOrder": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpsertSingleObjectValueCommand": {"type": "object", "properties": {"objectValues": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectValueData"}, "nullable": true}, "refId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "UpsertTenantContextCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpsertTenantLookupCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "tenantContextId": {"type": "string", "format": "uuid"}, "value": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "value1": {"type": "string", "nullable": true}, "value2": {"type": "string", "nullable": true}, "showSequence": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UserDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "externalUserId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "refreshTokenExpiryTime": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserDetailsDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetailsDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UserDetailsDtoObjectPagedResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetailsDto"}, "nullable": true}, "itemsCount": {"type": "integer", "format": "int32", "readOnly": true}, "totalCount": {"type": "integer", "format": "int32"}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UserDetailsDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserDetailsDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "externalUserId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "refreshTokenExpiryTime": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserDtoApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "UserDtoListApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}}, "additionalProperties": false}, "UserRoleDto": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "UserRoleDtoListApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}, "nullable": true}}, "additionalProperties": false}, "UserRoleUpdateRequest": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "roleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserRoleUpdateResult": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "assignedRoles": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ValidateHierarchicalObjectsQuery": {"required": ["objects", "productId"], "type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/HierarchicalObjectDto"}}}, "additionalProperties": false}, "ValueInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "refId": {"type": "string", "format": "uuid"}, "value": {"type": "string", "nullable": true}, "parentObjectValueId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ViewFieldMappingDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationId": {"type": "string", "format": "uuid"}, "apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "objectMetadataKey": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "userName": {"type": "string", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ViewFieldMappingDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewFieldMappingDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewFieldMappingDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewFieldMappingDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ViewFieldMappingDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ViewFieldMappingDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationApiDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ViewIntegrationApiDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationApiDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationApiDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationApiDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ViewIntegrationApiDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ViewIntegrationApiDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationConfigurationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationId": {"type": "string", "format": "uuid"}, "integrationName": {"type": "string", "nullable": true}, "integrationApiId": {"type": "string", "format": "uuid"}, "integrationApiName": {"type": "string", "nullable": true}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ViewIntegrationConfigurationDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationConfigurationDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ViewIntegrationConfigurationDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Input your Bearer token to access this API", "scheme": "Bearer", "bearerFormat": "JWT"}, "Tenant": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Input your tenant ID to access this API", "name": "tenant", "in": "header"}}}, "security": [{"Bearer": []}, {"Tenant": []}]}