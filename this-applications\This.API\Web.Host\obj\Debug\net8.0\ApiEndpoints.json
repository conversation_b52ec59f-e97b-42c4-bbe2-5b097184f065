[{"ContainingType": "Web.Host.Controllers.ComprehensiveEntityController", "Method": "GetHierarchicalEntityData", "RelativePath": "api/comprehensive-entity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "featureId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "onlyVisibleMetadata", "Type": "System.Boolean", "IsRequired": false}, {"Name": "onlyActiveMetadata", "Type": "System.Boolean", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.ComprehensiveEntityData.DTOs.HierarchicalEntityDataResponseDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ComprehensiveEntityController", "Method": "GetHierarchicalEntityDataByProduct", "RelativePath": "api/comprehensive-entity/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": true}, {"Name": "onlyVisibleMetadata", "Type": "System.Boolean", "IsRequired": false}, {"Name": "onlyActiveMetadata", "Type": "System.Boolean", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.ComprehensiveEntityData.DTOs.UnifiedHierarchicalEntityDataResponseDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ComprehensiveEntityController", "Method": "CreateProductStructure", "RelativePath": "api/comprehensive-entity/create-product-structure", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Comprehensive.Commands.CreateProductStructureCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Comprehensive.DTOs.ProductStructureCreationResult, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ComprehensiveEntityController", "Method": "GetComprehensiveSubscriptions", "RelativePath": "api/comprehensive-entity/subscriptions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantId", "Type": "System.String", "IsRequired": false}, {"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "subscriptionType", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isExpired", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pricingTier", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "startDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "startDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "expiringWithinDays", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "includeSummary", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.ComprehensiveEntityData.DTOs.ComprehensiveSubscriptionResponseDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetContextWithLookups", "RelativePath": "api/context/{contextId}/with-lookups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "contextId", "Type": "System.Guid", "IsRequired": true}, {"Name": "includeInactiveLookups", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetBulkContextWithLookups", "RelativePath": "api/context/bulk/with-lookups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "contextIds", "Type": "System.Collections.Generic.List`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}, {"Name": "includeInactiveLookups", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetAll", "RelativePath": "api/context/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetPaged", "RelativePath": "api/context/get-paged", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetTenantContext", "RelativePath": "api/context/get-tenant-context", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetLookup", "RelativePath": "api/context/lookup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "DeleteLookup", "RelativePath": "api/context/lookup/{lookupId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "lookupId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "BulkUpsertLookups", "RelativePath": "api/context/lookup/bulk-upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Context.Commands.UpsertBulkLookups.UpsertBulkLookupsCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "UpsertLookup", "RelativePath": "api/context/lookup/upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Context.Commands.UpsertLookup.UpsertLookupCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "DeleteObjectLookup", "RelativePath": "api/context/object-lookup/{objectLookupId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "objectLookupId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetAllObjectLookups", "RelativePath": "api/context/object-lookup/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "sourceType", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "objectId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "UpsertObjectLookup", "RelativePath": "api/context/object-lookup/upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.ObjectLookup.Commands.UpsertObjectLookup.UpsertObjectLookupCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetTenantContextWithLookups", "RelativePath": "api/context/tenant/{tenantContextId}/with-lookups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantContextId", "Type": "System.Guid", "IsRequired": true}, {"Name": "includeInactiveLookups", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetBulkTenantContextWithLookups", "RelativePath": "api/context/tenant/bulk/with-lookups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantContextIds", "Type": "System.Collections.Generic.List`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}, {"Name": "includeInactiveLookups", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetAllTenantContexts", "RelativePath": "api/context/tenant/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetPagedTenantContexts", "RelativePath": "api/context/tenant/get-paged", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "GetTenantContextLookup", "RelativePath": "api/context/tenant/lookup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "DeleteTenantLookup", "RelativePath": "api/context/tenant/lookup/{tenantLookupId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenantLookupId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "UpsertTenantLookup", "RelativePath": "api/context/tenant/lookup/upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Context.Commands.UpsertTenantLookup.UpsertTenantLookupCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "UpsertBulkTenantContext", "RelativePath": "api/context/tenant/upsert-bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Context.Commands.UpsertBulkTenantContext.UpsertBulkTenantContextCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "UpsertSingleTenantContext", "RelativePath": "api/context/tenant/upsert-single", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Context.Commands.UpsertTenantContext.UpsertTenantContextCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "UpsertBulk", "RelativePath": "api/context/upsert-bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Context.Commands.UpsertBulkContext.UpsertBulkContextCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ContextController", "Method": "UpsertSingle", "RelativePath": "api/context/upsert-single", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Context.Commands.UpsertContext.UpsertContextCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DataTransformationController", "Method": "TransformData", "RelativePath": "api/datatransformation/api/{apiName}/transform", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "apiName", "Type": "System.String", "IsRequired": true}, {"Name": "jsonObject", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DataTransformation.DTOs.DataTransformationResultDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Shared.Common.Response.Result`1[[Application.DataTransformation.DTOs.DataTransformationResultDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Shared.Common.Response.Result`1[[Application.DataTransformation.DTOs.DataTransformationResultDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "GetDataTypes", "RelativePath": "api/datatypes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Category", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "CreateDataType", "RelativePath": "api/datatypes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.DataTypes.Commands.CreateDataTypeCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "GetDataTypeById", "RelativePath": "api/datatypes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "UpdateDataType", "RelativePath": "api/datatypes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.DataTypes.Commands.UpdateDataTypeCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "DeleteDataType", "RelativePath": "api/datatypes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "GetDisplays", "RelativePath": "api/display", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.DisplayManagement.DTOs.DisplayDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "CreateDisplay", "RelativePath": "api/display", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.DisplayManagement.Commands.CreateDisplayCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DisplayManagement.DTOs.DisplayDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "GetDisplayById", "RelativePath": "api/display/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DisplayManagement.DTOs.DisplayDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "UpdateDisplay", "RelativePath": "api/display/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.DisplayManagement.Commands.UpdateDisplayCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DisplayManagement.DTOs.DisplayDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "DeleteDisplay", "RelativePath": "api/display/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "GetActions", "RelativePath": "api/display/actions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.ActionManagement.DTOs.ActionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "CreateAction", "RelativePath": "api/display/actions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.ActionManagement.Commands.CreateActionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.ActionManagement.DTOs.ActionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "GetActionById", "RelativePath": "api/display/actions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.ActionManagement.DTOs.ActionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "BulkUpsertDisplayWithActions", "RelativePath": "api/display/bulk-upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.DisplayManagement.DTOs.BulkDisplayWithActionsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DisplayManagement.DTOs.BulkDisplayWithActionsResponseDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DisplayController", "Method": "UpsertDisplayWithActions", "RelativePath": "api/display/upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.DisplayManagement.DTOs.DisplayWithActionsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DisplayManagement.DTOs.DisplayWithActionsResponseDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "GetFieldMappings", "RelativePath": "api/fieldmappings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "ApiName", "Type": "System.String", "IsRequired": false}, {"Name": "SourceType", "Type": "System.String", "IsRequired": false}, {"Name": "ObjectMetadataId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TargetObjectName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "CreateFieldMapping", "RelativePath": "api/fieldmappings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.FieldMappings.Commands.CreateFieldMappingCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "GetFieldMappingById", "RelativePath": "api/fieldmappings/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "UpdateFieldMapping", "RelativePath": "api/fieldmappings/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.FieldMappings.Commands.UpdateFieldMappingCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "DeleteFieldMapping", "RelativePath": "api/fieldmappings/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "CreateFieldMappings", "RelativePath": "api/fieldmappings/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.FieldMappings.Commands.CreateFieldMappingsCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "GetIntegrationApis", "RelativePath": "api/integrationapis", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProductId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "CreateIntegrationApi", "RelativePath": "api/integrationapis", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationApis.Commands.CreateIntegrationApiCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "GetIntegrationApiById", "RelativePath": "api/integrationapis/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "UpdateIntegrationApi", "RelativePath": "api/integrationapis/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.IntegrationApis.Commands.UpdateIntegrationApiCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "DeleteIntegrationApi", "RelativePath": "api/integrationapis/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "CreateIntegrationApis", "RelativePath": "api/integrationapis/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationApis.Commands.CreateIntegrationApisCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "CreateIntegrationApiWithConfiguration", "RelativePath": "api/integrationapis/with-configuration", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationApis.Commands.CreateIntegrationApiWithConfigurationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationApis.Commands.CreateIntegrationApiWithConfigurationResponseDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "GetIntegrationConfigurations", "RelativePath": "api/integrationconfigurations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IntegrationId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IntegrationApiId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ObjectId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Direction", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "CreateIntegrationConfiguration", "RelativePath": "api/integrationconfigurations", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationConfigurations.Commands.CreateIntegrationConfigurationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "GetIntegrationConfigurationById", "RelativePath": "api/integrationconfigurations/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "UpdateIntegrationConfiguration", "RelativePath": "api/integrationconfigurations/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.IntegrationConfigurations.Commands.UpdateIntegrationConfigurationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "DeleteIntegrationConfiguration", "RelativePath": "api/integrationconfigurations/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "CreateIntegrationConfigurations", "RelativePath": "api/integrationconfigurations/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationConfigurations.Commands.CreateIntegrationConfigurationsCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "GetIntegrationsWithApisByProductId", "RelativePath": "api/integrationconfigurations/integrations-with-apis/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": true}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.IntegrationConfigurations.Queries.IntegrationWithApiInfoDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "GetIntegrations", "RelativePath": "api/integrations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProductId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AuthType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "CreateIntegration", "RelativePath": "api/integrations", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Integrations.Commands.CreateIntegrationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "GetIntegrationById", "RelativePath": "api/integrations/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "UpdateIntegration", "RelativePath": "api/integrations/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.Integrations.Commands.UpdateIntegrationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "DeleteIntegration", "RelativePath": "api/integrations/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "CreateIntegrations", "RelativePath": "api/integrations/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Integrations.Commands.CreateIntegrationsCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "CreateIntegrationWithApi", "RelativePath": "api/integrations/with-api", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Integrations.Commands.CreateIntegrationWithApiCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Integrations.DTOs.CreateIntegrationWithApiResponseDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "GetMetadata", "RelativePath": "api/metadata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "DataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisible", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "CreateMetadata", "RelativePath": "api/metadata", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.MetadataManagement.Commands.CreateMetadataCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "GetMetadataById", "RelativePath": "api/metadata/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "UpdateMetadata", "RelativePath": "api/metadata/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.MetadataManagement.Commands.UpdateMetadataCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "DeleteMetadata", "RelativePath": "api/metadata/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "GetObjects", "RelativePath": "api/objects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "FeatureId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Objects.DTOs.ObjectDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "CreateObject", "RelativePath": "api/objects", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Objects.Commands.CreateObjectCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Objects.DTOs.ObjectDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "GetObjectById", "RelativePath": "api/objects/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Objects.DTOs.ObjectDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "GetObjectMetadata", "RelativePath": "api/objects/{id}/metadata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.ObjectMetadataManagement.DTOs.ObjectMetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "GetObjectByIdWithMetadata", "RelativePath": "api/objects/{id}/with-metadata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Objects.DTOs.ObjectWithMetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "CreateHierarchicalObjects", "RelativePath": "api/objects/bulk-create-hierarchical", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Objects.Commands.CreateHierarchicalObjectsCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Objects.DTOs.HierarchicalObjectCreationResult, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "ValidateHierarchicalObjects", "RelativePath": "api/objects/validate-hierarchical", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "Application.Objects.Commands.ValidateHierarchicalObjectsQuery", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Objects.DTOs.HierarchicalObjectValidationResult, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "GetObjectsWithActions", "RelativePath": "api/objects/with-actions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ProductId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ParentObjectId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.Objects.DTOs.ObjectWithActionsDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "GetObjectsWithMetadata", "RelativePath": "api/objects/with-metadata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "FeatureId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Objects.DTOs.ObjectWithMetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "GetObjectInstanceViewByRefId", "RelativePath": "api/objectvalues/instance-view-by-refid/{refId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "refId", "Type": "System.Guid", "IsRequired": true}, {"Name": "viewName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "GetObjectInstanceView", "RelativePath": "api/objectvalues/instances-view/{objectName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "objectName", "Type": "System.String", "IsRequired": true}, {"Name": "createView", "Type": "System.Boolean", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "GetMetadataKeyValues", "RelativePath": "api/objectvalues/metadata-key-values/{objectName}/{metadataKey}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "objectName", "Type": "System.String", "IsRequired": true}, {"Name": "metadataKey", "Type": "System.String", "IsRequired": true}, {"Name": "createView", "Type": "System.Boolean", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "GetObjectLookupData", "RelativePath": "api/objectvalues/object-lookup-data/{objectLookupId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "objectLookupId", "Type": "System.Guid", "IsRequired": true}, {"Name": "createView", "Type": "System.Boolean", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "SingleValueUpdate", "RelativePath": "api/objectvalues/single-value-update", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Shared.SingleValueUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "SingleValueUpdateByRefId", "RelativePath": "api/objectvalues/single-value-update-by-refid", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Shared.SingleValueUpdateByRefIdRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "UpsertBulk", "RelativePath": "api/objectvalues/upsert-bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.ObjectValues.Commands.UpsertBulk.UpsertBulkObjectValueCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "UpsertBulkWithMetadata", "RelativePath": "api/objectvalues/upsert-bulk-with-metadata", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Shared.ObjectUpsertWithMetadataBulkRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "UpsertSingle", "RelativePath": "api/objectvalues/upsert-single", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.ObjectValues.Commands.UpsertSingle.UpsertSingleObjectValueCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ObjectValuesController", "Method": "UpsertSingleWithMetadata", "RelativePath": "api/objectvalues/upsert-single-with-metadata", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Shared.ObjectUpsertWithMetadataRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "GetProducts", "RelativePath": "api/products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "CreateProduct", "RelativePath": "api/products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Products.Commands.CreateProductCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "GetProductById", "RelativePath": "api/products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "UpdateProduct", "RelativePath": "api/products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.Products.Commands.UpdateProductCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "CreateProductWithSubscription", "RelativePath": "api/products/with-subscription", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Products.Commands.CreateProductWithSubscriptionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductWithSubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "GetAllAsync", "RelativePath": "api/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Abstraction.Identity.Dtos.RoleDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "CreateAsync", "RelativePath": "api/roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.CreateRoleRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "UpdateAsync", "RelativePath": "api/roles", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.UpdateRoleRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "GetByIdAsync", "RelativePath": "api/roles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.RoleDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "DeleteAsync", "RelativePath": "api/roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "GetRoleActionsAsync", "RelativePath": "api/roles/{roleId}/actions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.RoleActions.DTOs.RoleActionsResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "UpdateRoleActionsAsync", "RelativePath": "api/roles/{roleId}/actions", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Guid", "IsRequired": true}, {"Name": "actionIds", "Type": "System.Collections.Generic.List`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.RoleActions.DTOs.RoleActionsResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "BulkUpdateRoleActionsAsync", "RelativePath": "api/roles/bulk/actions", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Application.RoleActions.DTOs.BulkUpdateRoleActionsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.RoleActions.DTOs.RoleActionsResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "GetSubscriptions", "RelativePath": "api/subscriptions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "ProductId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "CreateSubscription", "RelativePath": "api/subscriptions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Application.Subscriptions.DTOs.CreateSubscriptionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "GetSubscriptionById", "RelativePath": "api/subscriptions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "UpdateSubscription", "RelativePath": "api/subscriptions/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "Application.Subscriptions.DTOs.UpdateSubscriptionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "DeleteSubscription", "RelativePath": "api/subscriptions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "GetActiveSubscriptions", "RelativePath": "api/subscriptions/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "GetSubscriptionsByProduct", "RelativePath": "api/subscriptions/by-product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": true}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "GetSubscriptionsByStatus", "RelativePath": "api/subscriptions/by-status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.SubscriptionsController", "Method": "GetExpiredSubscriptions", "RelativePath": "api/subscriptions/expired", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Subscriptions.DTOs.SubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "GetTemplates", "RelativePath": "api/templates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Stage", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IncludeDeleted", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Templates.DTOs.TemplateDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "CreateTemplate", "RelativePath": "api/templates", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Application.Templates.DTOs.CreateTemplateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Templates.DTOs.TemplateDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "GetTemplateById", "RelativePath": "api/templates/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Templates.DTOs.TemplateDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "UpdateTemplate", "RelativePath": "api/templates/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "Application.Templates.DTOs.UpdateTemplateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Templates.DTOs.TemplateDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "DeleteTemplate", "RelativePath": "api/templates/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "PublishTemplate", "RelativePath": "api/templates/{id}/publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Templates.DTOs.TemplateDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "GetTemplatesByProduct", "RelativePath": "api/templates/by-product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Guid", "IsRequired": true}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "stage", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Templates.DTOs.TemplateDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.TemplatesController", "Method": "GetTemplatesByStage", "RelativePath": "api/templates/by-stage/{stage}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stage", "Type": "System.String", "IsRequired": true}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "productId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Templates.DTOs.TemplateDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "GetAllAsync", "RelativePath": "api/tenants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Abstraction.MultiTenancy.Dtos.TenantDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "CreateAsync", "RelativePath": "api/tenants", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.MultiTenancy.Dtos.TenantDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "UpdateAsync", "RelativePath": "api/tenants", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.MultiTenancy.Dtos.TenantDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "GetByIdAsync", "RelativePath": "api/tenants/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MultiTenancy.DTOs.TenantWithProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "DeleteAsync", "RelativePath": "api/tenants/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "ActivateAsync", "RelativePath": "api/tenants/{id}/activate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "DeactivateAsync", "RelativePath": "api/tenants/{id}/deactivate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "UpsertAsync", "RelativePath": "api/tenants/upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.MultiTenancy.Dtos.TenantDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.MultiTenancy.Dtos.TenantDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.TokensController", "Method": "GetTokenAsync", "RelativePath": "api/tokens", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.TokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.TokenResponse, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.TokensController", "Method": "RefreshAsync", "RelativePath": "api/tokens/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.TokenResponse, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetAllAsync", "RelativePath": "api/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Abstraction.Identity.Dtos.UserDetailsDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "RegisterAsync", "RelativePath": "api/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.RegisterUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetByIdAsync", "RelativePath": "api/users/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.UserDetailsDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "UpdateAsync", "RelativePath": "api/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.UpdateUserRequest", "IsRequired": true}, {"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "ChangePasswordAsync", "RelativePath": "api/users/{id}/change-password", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.ChangePasswordRequest", "IsRequired": true}, {"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetUserRolesAsync", "RelativePath": "api/users/{userId}/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.Collections.Generic.List`1[[Application.Identity.DTOs.UserRoleDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "UpdateUserRolesAsync", "RelativePath": "api/users/{userId}/roles", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "userRoles", "Type": "System.Collections.Generic.List`1[[Application.Identity.DTOs.UserRoleDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "AssignRolesToUserAsync", "RelativePath": "api/users/{userId}/roles/assign", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "roleNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "RemoveRolesFromUserAsync", "RelativePath": "api/users/{userId}/roles/remove", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "roleNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "BulkCreateUsersAsync", "RelativePath": "api/users/bulk-create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Identity.Commands.BulkCreateUsersCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[Application.Identity.Commands.BulkCreateUsersResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "BulkUpdateUserRolesAsync", "RelativePath": "api/users/bulk-update-roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Identity.Commands.BulkUpdateUserRolesCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[Application.Identity.Commands.BulkUpdateUserRolesResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "CreateUserAsync", "RelativePath": "api/users/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Identity.Commands.CreateUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[Application.Identity.DTOs.UserDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "ForgotPasswordAsync", "RelativePath": "api/users/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetPaginatedAsync", "RelativePath": "api/users/paginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchString", "Type": "System.String", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PagedResponse`2[[Abstraction.Identity.Dtos.UserDetailsDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "ResetPasswordAsync", "RelativePath": "api/users/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetUsersInRoleAsync", "RelativePath": "api/users/roles/{roleName}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.Collections.Generic.List`1[[Application.Identity.DTOs.UserDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]